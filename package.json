{"name": "patient-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "db:reset": "npm run db:drop && npm run db:generate && npm run db:migrate", "db:seed": "tsx scripts/seed.ts", "db:backup": "pg_dump $DATABASE_URL > backup.sql", "db:restore": "psql $DATABASE_URL < backup.sql"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@types/crypto-js": "^4.2.2", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "firebase": "^9.23.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "nodemailer": "^7.0.4", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.10.5", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "tsx": "^4.20.3", "typescript": "^5.3.3"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}