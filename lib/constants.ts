import { UserRole } from './types'

// Dashboard routes for different user roles
export const DASHBOARD_ROUTES: Record<UserRole, string> = {
  admin: '/dashboard/admin',
  doctor: '/dashboard/doctor',
  patient: '/dashboard/patient',
}

// API endpoints
export const API_ENDPOINTS = {
  auth: {
    login: '/api/auth/login',
    register: '/api/auth/register',
    logout: '/api/auth/logout',
    refresh: '/api/auth/refresh',
  },
  users: {
    list: '/api/users',
    create: '/api/users',
    update: (id: string) => `/api/users/${id}`,
    delete: (id: string) => `/api/users/${id}`,
  },
  patients: {
    list: '/api/patients',
    create: '/api/patients',
    update: (id: string) => `/api/patients/${id}`,
    delete: (id: string) => `/api/patients/${id}`,
  },
  doctors: {
    list: '/api/doctors',
    create: '/api/doctors',
    update: (id: string) => `/api/doctors/${id}`,
    delete: (id: string) => `/api/doctors/${id}`,
  },
  appointments: {
    list: '/api/appointments',
    create: '/api/appointments',
    update: (id: string) => `/api/appointments/${id}`,
    delete: (id: string) => `/api/appointments/${id}`,
  },
  medicalRecords: {
    list: '/api/medical-records',
    create: '/api/medical-records',
    update: (id: string) => `/api/medical-records/${id}`,
    delete: (id: string) => `/api/medical-records/${id}`,
  },
}

// Application settings
export const APP_CONFIG = {
  name: 'Patient Management System',
  version: '1.0.0',
  description: 'A comprehensive healthcare management platform',
  supportEmail: '<EMAIL>',
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
}

// Pagination settings
export const PAGINATION = {
  defaultPageSize: 10,
  maxPageSize: 100,
  pageSizeOptions: [10, 25, 50, 100],
}

// Form validation rules
export const VALIDATION_RULES = {
  email: {
    required: 'Email is required',
    invalid: 'Please enter a valid email address',
  },
  password: {
    required: 'Password is required',
    minLength: 'Password must be at least 6 characters',
    weak: 'Password should contain at least one uppercase letter, one lowercase letter, and one number',
  },
  name: {
    required: 'Name is required',
    minLength: 'Name must be at least 2 characters',
    maxLength: 'Name cannot exceed 50 characters',
  },
  phone: {
    required: 'Phone number is required',
    invalid: 'Please enter a valid phone number',
  },
}

// Status options
export const STATUS_OPTIONS = {
  appointment: [
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'no-show', label: 'No Show' },
  ],
  user: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'pending', label: 'Pending' },
    { value: 'suspended', label: 'Suspended' },
  ],
  medicalRecord: [
    { value: 'draft', label: 'Draft' },
    { value: 'completed', label: 'Completed' },
    { value: 'reviewed', label: 'Reviewed' },
    { value: 'archived', label: 'Archived' },
  ],
}

// Time slots for appointments
export const TIME_SLOTS = [
  '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
  '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',
  '17:00', '17:30', '18:00', '18:30', '19:00', '19:30',
]

// Medical specializations
export const MEDICAL_SPECIALIZATIONS = [
  'Internal Medicine',
  'Cardiology',
  'Dermatology',
  'Endocrinology',
  'Gastroenterology',
  'Neurology',
  'Oncology',
  'Orthopedics',
  'Pediatrics',
  'Psychiatry',
  'Radiology',
  'Surgery',
  'Urology',
  'Emergency Medicine',
  'Family Medicine',
  'Anesthesiology',
  'Pathology',
  'Ophthalmology',
  'Otolaryngology',
  'Pulmonology',
]

// Error messages
export const ERROR_MESSAGES = {
  generic: 'An unexpected error occurred. Please try again.',
  network: 'Network error. Please check your connection and try again.',
  unauthorized: 'You are not authorized to perform this action.',
  forbidden: 'Access denied. You do not have permission to access this resource.',
  notFound: 'The requested resource was not found.',
  validation: 'Please check your input and try again.',
  server: 'Server error. Please try again later.',
}
