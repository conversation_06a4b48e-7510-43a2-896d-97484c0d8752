import { drizzle } from 'drizzle-orm/postgres-js'
import postgres = require('postgres')
import * as schema from './schema'

// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL']
const missingEnvVars = requiredEnvVars.filter(
  (envVar) => !process.env[envVar]
)

if (missingEnvVars.length > 0 && process.env.NODE_ENV !== 'test') {
  console.warn(`Missing database environment variables: ${missingEnvVars.join(', ')}`)
  console.warn('Database functionality will be limited. Please configure your environment variables.')
}

// Create the connection
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/pms_dev'

// For serverless environments, we need to configure the connection properly
const client = postgres(connectionString, {
  prepare: false,
  max: process.env.NODE_ENV === 'production' ? 1 : 10,
})

export const db = drizzle(client, { schema })

// Export the client for direct queries if needed
export { client }

// Test connection function
export async function testConnection() {
  try {
    await client`SELECT 1`
    console.log('✅ Database connection successful')
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}

// Close connection function
export async function closeConnection() {
  try {
    await client.end()
    console.log('Database connection closed')
  } catch (error) {
    console.error('Error closing database connection:', error)
  }
}
