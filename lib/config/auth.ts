/**
 * Authentication configuration for next-firebase-auth-edge
 * This configuration is used by the API authentication middleware
 */

export interface AuthConfigInstance {
  apiKey: string
  cookieName: string
  cookieSignatureKeys: string[]
  serviceAccount: {
    projectId: string
    clientEmail: string
    privateKey: string
  }
}

/**
 * Get authentication configuration instance for next-firebase-auth-edge
 */
export function getAuthConfigInstance(): AuthConfigInstance {
  // Validate required environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'FIREBASE_SERVICE_ACCOUNT_CLIENT_EMAIL',
    'FIREBASE_SERVICE_ACCOUNT_PRIVATE_KEY',
    'COOKIE_SECRET_CURRENT',
  ]

  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  )

  if (missingEnvVars.length > 0) {
    console.error(`Missing required environment variables for authentication: ${missingEnvVars.join(', ')}`)
    throw new Error(`Authentication configuration incomplete. Missing: ${missingEnvVars.join(', ')}`)
  }

  // Parse private key (handle escaped newlines)
  const privateKey = process.env.FIREBASE_SERVICE_ACCOUNT_PRIVATE_KEY!.replace(/\\n/g, '\n')

  // Parse cookie signature keys (can be comma-separated for rotation)
  const cookieSignatureKeys = process.env.COOKIE_SECRET_CURRENT!.split(',').map(key => key.trim())

  return {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
    cookieName: process.env.AUTH_COOKIE_NAME || 'AuthToken',
    cookieSignatureKeys,
    serviceAccount: {
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
      clientEmail: process.env.FIREBASE_SERVICE_ACCOUNT_CLIENT_EMAIL!,
      privateKey,
    },
  }
}

/**
 * Validate authentication configuration
 */
export function validateAuthConfig(): boolean {
  try {
    getAuthConfigInstance()
    return true
  } catch (error) {
    console.error('Authentication configuration validation failed:', error)
    return false
  }
}
