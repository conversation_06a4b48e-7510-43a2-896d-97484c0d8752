'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { AuthContextType, User, LoginCredentials, RegisterData } from '../types'
import { getAuthAdapter } from '../services/auth.service'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const authAdapter = getAuthAdapter()

  useEffect(() => {
    const unsubscribe = authAdapter.onAuthStateChange((user) => {
      setUser(user)
      setLoading(false)
    })

    return unsubscribe
  }, [])

  const login = async (credentials: LoginCredentials) => {
    try {
      setLoading(true)
      setError(null)
      const user = await authAdapter.login(credentials)
      setUser(user)
    } catch (error: any) {
      setError(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const register = async (data: RegisterData, invitationToken?: string) => {
    try {
      setLoading(true)
      setError(null)

      let user: User
      if (invitationToken) {
        // Use invitation-based registration
        user = await authAdapter.registerWithInvitation(data, invitationToken)
      } else {
        // Use regular registration (patient-first)
        user = await authAdapter.register(data)
      }

      setUser(user)
    } catch (error: any) {
      setError(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const loginWithGoogle = async () => {
    try {
      setLoading(true)
      setError(null)
      const user = await authAdapter.loginWithGoogle()
      setUser(user)
    } catch (error: any) {
      setError(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      setError(null)
      await authAdapter.logout()
      setUser(null)
    } catch (error: any) {
      setError(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const clearError = () => {
    setError(null)
  }

  const value: AuthContextType = {
    user,
    loading,
    error,
    login,
    register,
    loginWithGoogle,
    logout,
    clearError,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
