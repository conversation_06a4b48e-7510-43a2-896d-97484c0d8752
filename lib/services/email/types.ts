export interface EmailOptions {
  to: string
  subject: string
  html?: string
  text?: string
}

export interface EmailAdapter {
  sendEmail(options: EmailOptions): Promise<boolean>
  testConnection(): Promise<boolean>
  sendDoctorInvitation(email: string, token: string, invitedByName: string): Promise<boolean>
}

export type EmailProvider = 'nodemailer' | 'sendgrid' | 'ses' | 'resend'

export interface EmailConfig {
  provider: EmailProvider
  nodemailerConfig?: {
    host: string
    port: number
    secure: boolean
    auth: {
      user: string
      pass: string
    }
  }
  sendgridConfig?: {
    apiKey: string
    fromEmail: string
    fromName: string
  }
  sesConfig?: {
    region: string
    accessKeyId: string
    secretAccessKey: string
    fromEmail: string
  }
  resendConfig?: {
    apiKey: string
    fromEmail: string
    fromName: string
  }
}
