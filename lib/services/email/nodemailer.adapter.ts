import nodemailer from 'nodemailer'
import { Em<PERSON>Adapter, EmailOptions } from './types'

interface NodemailerConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
}

export class NodemailerEmailAdapter implements EmailAdapter {
  private transporter: nodemailer.Transporter | null = null

  constructor() {
    this.initializeTransporter()
  }

  private initializeTransporter() {
    try {
      const config: NodemailerConfig = {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: false,
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || '',
        },
      }

      // Only initialize if we have the required environment variables
      if (config.auth.user && config.auth.pass) {
        this.transporter = nodemailer.createTransport(config)
      } else {
        console.warn('Email service not configured. Missing SMTP credentials.')
      }
    } catch (error) {
      console.error('Failed to initialize email transporter:', error)
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      console.error('Email transporter not initialized')
      return false
    }

    try {
      const mailOptions = {
        from: `${process.env.FROM_NAME || 'Patient Management System'} <${process.env.FROM_EMAIL || process.env.SMTP_USER}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Email sent successfully:', result.messageId)
      return true
    } catch (error) {
      console.error('Failed to send email:', error)
      return false
    }
  }

  async testConnection(): Promise<boolean> {
    if (!this.transporter) {
      console.error('Email transporter not initialized')
      return false
    }

    try {
      await this.transporter.verify()
      console.log('Email service connection verified')
      return true
    } catch (error) {
      console.error('Email service connection failed:', error)
      return false
    }
  }

  async sendDoctorInvitation(email: string, token: string, invitedByName: string): Promise<boolean> {
    const registrationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/signup?invitation=${token}`

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Doctor Invitation - Patient Management System</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background-color: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; font-size: 14px; color: #64748b; }
          .warning { background-color: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Doctor Invitation</h1>
            <p>Patient Management System</p>
          </div>
          <div class="content">
            <h2>Welcome to the Team!</h2>
            <p>Hello,</p>
            <p><strong>${invitedByName}</strong> has invited you to join the Patient Management System as a Doctor.</p>
            <p>To complete your registration and start using the system, please click the button below:</p>
            <div style="text-align: center;">
              <a href="${registrationUrl}" class="button">Complete Registration</a>
            </div>
            <div class="warning">
              <h3>Important Information:</h3>
              <ul>
                <li>This invitation link will expire in <strong>7 days</strong></li>
                <li>The link can only be used <strong>once</strong></li>
                <li>You must use the email address: <strong>${email}</strong></li>
              </ul>
            </div>
            <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background-color: #f1f5f9; padding: 10px; border-radius: 4px; font-family: monospace;">
              ${registrationUrl}
            </p>
            <p>If you have any questions or need assistance, please contact the system administrator.</p>
            <p>We're excited to have you join our healthcare team!</p>
          </div>
          <div class="footer">
            <p>This email was sent by the Patient Management System. If you received this email in error, please ignore it.</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
      Doctor Invitation - Patient Management System
      
      Hello,
      
      ${invitedByName} has invited you to join the Patient Management System as a Doctor.
      
      To complete your registration, please visit:
      ${registrationUrl}
      
      Important:
      - This invitation link will expire in 7 days
      - The link can only be used once
      - You must use the email address: ${email}
      
      If you have any questions, please contact the system administrator.
      
      Welcome to the team!
    `

    return this.sendEmail({
      to: email,
      subject: 'Doctor Invitation - Patient Management System',
      html,
      text,
    })
  }
}
