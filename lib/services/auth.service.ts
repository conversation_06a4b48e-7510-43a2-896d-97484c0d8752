import { AuthAdapter, AuthProvider, AuthConfig } from './auth/types'
import { FirebaseAuthAdapter } from './auth/firebase.adapter'

/**
 * Factory function to create the appropriate auth adapter based on environment configuration
 */
function createAuthAdapter(): AuthAdapter {
  const authProvider = (process.env.AUTH_PROVIDER || 'firebase') as AuthProvider
  
  switch (authProvider) {
    case 'firebase':
      return new FirebaseAuthAdapter()
    case 'auth0':
      throw new Error('Auth0 adapter not implemented yet')
    case 'supabase':
      throw new Error('Supabase adapter not implemented yet')
    default:
      console.warn(`Unknown auth provider: ${authProvider}. Falling back to Firebase.`)
      return new FirebaseAuthAdapter()
  }
}

/**
 * Get auth configuration from environment variables
 */
export function getAuthConfig(): AuthConfig {
  const authProvider = (process.env.AUTH_PROVIDER || 'firebase') as AuthProvider
  
  return {
    provider: authProvider,
    firebaseConfig: authProvider === 'firebase' ? {
      // Firebase config is handled in lib/config/firebase.ts
    } : undefined,
    auth0Config: authProvider === 'auth0' ? {
      domain: process.env.AUTH0_DOMAIN,
      clientId: process.env.AUTH0_CLIENT_ID,
      clientSecret: process.env.AUTH0_CLIENT_SECRET,
    } : undefined,
    supabaseConfig: authProvider === 'supabase' ? {
      url: process.env.SUPABASE_URL,
      anonKey: process.env.SUPABASE_ANON_KEY,
    } : undefined,
  }
}

/**
 * Singleton instance of the auth adapter
 * This ensures we use the same adapter instance throughout the application
 */
let authAdapterInstance: AuthAdapter | null = null

/**
 * Get the auth adapter instance
 * Creates a new instance if one doesn't exist
 */
export function getAuthAdapter(): AuthAdapter {
  if (!authAdapterInstance) {
    authAdapterInstance = createAuthAdapter()
  }
  return authAdapterInstance
}

/**
 * Reset the auth adapter instance (useful for testing)
 */
export function resetAuthAdapter(): void {
  authAdapterInstance = null
}

// Export types for use in other modules
export type { AuthAdapter, AuthProvider, AuthConfig } from './auth/types'

// Export adapter classes for direct use if needed
export { FirebaseAuthAdapter } from './auth/firebase.adapter'

// Default export is the adapter instance
export default getAuthAdapter()
