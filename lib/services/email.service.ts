import { Email<PERSON><PERSON>pter, <PERSON><PERSON><PERSON>rovider, EmailConfig } from './email/types'
import { NodemailerEmailAdapter } from './email/nodemailer.adapter'

/**
 * Factory function to create the appropriate email adapter based on environment configuration
 */
function createEmailAdapter(): EmailAdapter {
  const emailProvider = (process.env.EMAIL_PROVIDER || 'nodemailer') as Email<PERSON>rovider

  switch (emailProvider) {
    case 'nodemailer':
      return new NodemailerEmailAdapter()
    case 'sendgrid':
      throw new Error('SendGrid adapter not implemented yet')
    case 'ses':
      throw new Error('AWS SES adapter not implemented yet')
    case 'resend':
      throw new Error('Resend adapter not implemented yet')
    default:
      console.warn(`Unknown email provider: ${emailProvider}. Falling back to Nodemailer.`)
      return new NodemailerEmailAdapter()
  }
}

/**
 * Get email configuration from environment variables
 */
export function getEmailConfig(): EmailConfig {
  const emailProvider = (process.env.EMAIL_PROVIDER || 'nodemailer') as EmailProvider

  return {
    provider: emailProvider,
    nodemailerConfig: emailProvider === 'nodemailer' ? {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
    } : undefined,
    sendgridConfig: emailProvider === 'sendgrid' ? {
      apiKey: process.env.SENDGRID_API_KEY || '',
      fromEmail: process.env.FROM_EMAIL || '',
      fromName: process.env.FROM_NAME || 'Patient Management System',
    } : undefined,
    sesConfig: emailProvider === 'ses' ? {
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      fromEmail: process.env.FROM_EMAIL || '',
    } : undefined,
    resendConfig: emailProvider === 'resend' ? {
      apiKey: process.env.RESEND_API_KEY || '',
      fromEmail: process.env.FROM_EMAIL || '',
      fromName: process.env.FROM_NAME || 'Patient Management System',
    } : undefined,
  }
}

/**
 * Singleton instance of the email adapter
 * This ensures we use the same adapter instance throughout the application
 */
let emailAdapterInstance: EmailAdapter | null = null

/**
 * Get the email adapter instance
 * Creates a new instance if one doesn't exist
 */
export function getEmailAdapter(): EmailAdapter {
  if (!emailAdapterInstance) {
    emailAdapterInstance = createEmailAdapter()
  }
  return emailAdapterInstance
}

/**
 * Reset the email adapter instance (useful for testing)
 */
export function resetEmailAdapter(): void {
  emailAdapterInstance = null
}

class EmailService {
  private adapter: EmailAdapter

  constructor() {
    this.adapter = getEmailAdapter()
  }

  async sendEmail(options: { to: string; subject: string; html?: string; text?: string }): Promise<boolean> {
    return this.adapter.sendEmail(options)
  }

  async testConnection(): Promise<boolean> {
    return this.adapter.testConnection()
  }

  async sendDoctorInvitation(email: string, token: string, invitedByName: string): Promise<boolean> {
    return this.adapter.sendDoctorInvitation(email, token, invitedByName)
  }
}

// Export types for use in other modules
export type { EmailAdapter, EmailProvider, EmailConfig } from './email/types'

// Export adapter classes for direct use if needed
export { NodemailerEmailAdapter } from './email/nodemailer.adapter'

// Default export is the service instance
export default EmailService
