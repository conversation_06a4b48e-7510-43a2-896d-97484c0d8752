import { getDatabaseAdapter } from '../database'
import EmailService from './email.service'
import { User } from '../types'

interface InvitationResult {
  success: boolean
  message: string
  token?: string
  expiresAt?: Date
}

interface InvitationValidation {
  isValid: boolean
  message: string
  invitation?: any
}

class InvitationService {
  private dataAdapter = getDatabaseAdapter()
  private emailService: EmailService

  constructor() {
    this.emailService = new EmailService()
  }

  async inviteDoctor(email: string, invitedBy: User): Promise<InvitationResult> {
    try {
      // Validate that the inviter is an admin
      if (invitedBy.role !== 'admin') {
        return {
          success: false,
          message: 'Only administrators can invite doctors',
        }
      }

      // Check if user already exists
      const existingUser = await this.dataAdapter.getUserByFirebaseUid(email)
      if (existingUser) {
        return {
          success: false,
          message: 'A user with this email already exists',
        }
      }

      // Create invitation
      const { token, expiresAt } = await this.dataAdapter.createDoctorInvitation(
        email,
        invitedBy.uid
      )

      // Send invitation email
      const emailSent = await this.emailService.sendDoctorInvitation(
        email,
        token,
        invitedBy.displayName || 'Admin'
      )

      if (!emailSent) {
        return {
          success: false,
          message: 'Failed to send invitation email. Please check email configuration.',
        }
      }

      return {
        success: true,
        message: 'Doctor invitation sent successfully',
        token,
        expiresAt,
      }
    } catch (error) {
      console.error('Error inviting doctor:', error)
      return {
        success: false,
        message: 'Failed to send invitation. Please try again.',
      }
    }
  }

  async validateInvitation(token: string): Promise<InvitationValidation> {
    try {
      if (!token) {
        return {
          isValid: false,
          message: 'Invitation token is required',
        }
      }

      const invitation = await this.dataAdapter.getDoctorInvitationByToken(token)

      if (!invitation) {
        return {
          isValid: false,
          message: 'Invalid or expired invitation token',
        }
      }

      if (invitation.isUsed) {
        return {
          isValid: false,
          message: 'This invitation has already been used',
        }
      }

      if (new Date() > new Date(invitation.expiresAt)) {
        return {
          isValid: false,
          message: 'This invitation has expired',
        }
      }

      return {
        isValid: true,
        message: 'Invitation is valid',
        invitation,
      }
    } catch (error) {
      console.error('Error validating invitation:', error)
      return {
        isValid: false,
        message: 'Failed to validate invitation',
      }
    }
  }

  async getInvitationEmail(token: string): Promise<string | null> {
    try {
      const invitation = await this.dataAdapter.getDoctorInvitationByToken(token)
      return invitation?.email || null
    } catch (error) {
      console.error('Error getting invitation email:', error)
      return null
    }
  }

  async markInvitationAsUsed(token: string): Promise<boolean> {
    try {
      await this.dataAdapter.markInvitationAsUsed(token)
      return true
    } catch (error) {
      console.error('Error marking invitation as used:', error)
      return false
    }
  }

  async resendInvitation(email: string, invitedBy: User): Promise<InvitationResult> {
    try {
      // Validate that the inviter is an admin
      if (invitedBy.role !== 'admin') {
        return {
          success: false,
          message: 'Only administrators can resend invitations',
        }
      }

      // For simplicity, we'll create a new invitation
      // In a production system, you might want to invalidate the old one first
      return this.inviteDoctor(email, invitedBy)
    } catch (error) {
      console.error('Error resending invitation:', error)
      return {
        success: false,
        message: 'Failed to resend invitation. Please try again.',
      }
    }
  }

  async testEmailService(): Promise<boolean> {
    return this.emailService.testConnection()
  }
}

export default InvitationService
