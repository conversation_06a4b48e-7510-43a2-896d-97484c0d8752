import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  UserCredential,
} from 'firebase/auth'
import { auth } from '../../config/firebase'
import { User, UserRole, LoginCredentials, RegisterData } from '../../types'
import { AuthAdapter } from './types'

export class FirebaseAuthAdapter implements AuthAdapter {
  constructor() {
    // No direct database adapter - using API calls instead
  }

  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const userCredential: UserCredential = await signInWithEmailAndPassword(
        auth,
        credentials.email,
        credentials.password
      )

      const response = await fetch(`/api/auth/user?firebaseUid=${userCredential.user.uid}`)
      if (!response.ok) {
        throw new Error('User data not found')
      }

      const { user } = await response.json()
      return user
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error.code))
    }
  }

  async register(data: RegisterData): Promise<User> {
    try {
      const userCredential: UserCredential = await createUserWithEmailAndPassword(
        auth,
        data.email,
        data.password
      )

      // Always assign patient role for default signups
      const userData = {
        firebaseUid: userCredential.user.uid,
        email: data.email,
        displayName: data.displayName,
        role: 'patient' as UserRole, // Patient-first signup
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userData })
      })

      if (!response.ok) {
        throw new Error('Failed to create user record')
      }

      const { user } = await response.json()
      return user
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error.code))
    }
  }

  async registerWithInvitation(data: RegisterData, invitationToken: string): Promise<User> {
    try {
      const userCredential: UserCredential = await createUserWithEmailAndPassword(
        auth,
        data.email,
        data.password
      )

      // Create user with doctor role
      const userData = {
        firebaseUid: userCredential.user.uid,
        email: data.email,
        displayName: data.displayName,
        role: 'doctor' as UserRole, // Doctor role for invited users
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userData, invitationToken })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to register with invitation')
      }

      const { user } = await response.json()
      return user
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error.code))
    }
  }

  async loginWithGoogle(): Promise<User> {
    try {
      const provider = new GoogleAuthProvider()
      const userCredential: UserCredential = await signInWithPopup(auth, provider)

      // Check if user exists in our database
      const response = await fetch(`/api/auth/user?firebaseUid=${userCredential.user.uid}`)
      
      if (response.ok) {
        const { user } = await response.json()
        return user
      } else {
        // User doesn't exist, create new user with patient role
        const userData = {
          firebaseUid: userCredential.user.uid,
          email: userCredential.user.email!,
          displayName: userCredential.user.displayName || 'User',
          role: 'patient' as UserRole, // Patient-first signup
          createdAt: new Date(),
          updatedAt: new Date(),
        }

        const createResponse = await fetch('/api/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userData })
        })

        if (!createResponse.ok) {
          throw new Error('Failed to create user record')
        }

        const { user } = await createResponse.json()
        return user
      }
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error.code))
    }
  }

  async logout(): Promise<void> {
    try {
      await signOut(auth)
    } catch (error: any) {
      throw new Error('Failed to logout')
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const firebaseUser = auth.currentUser
      if (!firebaseUser) return null

      const response = await fetch(`/api/auth/user?firebaseUid=${firebaseUser.uid}`)
      if (!response.ok) {
        return null
      }

      const { user } = await response.json()
      return user
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  onAuthStateChange(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        try {
          const response = await fetch(`/api/auth/user?firebaseUid=${firebaseUser.uid}`)
          if (response.ok) {
            const { user } = await response.json()
            callback(user)
          } else {
            callback(null)
          }
        } catch (error) {
          console.error('Error in auth state change:', error)
          callback(null)
        }
      } else {
        callback(null)
      }
    })
  }

  async updateUserRole(uid: string, role: UserRole): Promise<void> {
    try {
      const response = await fetch('/api/auth/update-role', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid, role })
      })

      if (!response.ok) {
        throw new Error('Failed to update user role')
      }
    } catch (error) {
      console.error('Error updating user role:', error)
      throw new Error('Failed to update user role')
    }
  }

  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'No user found with this email address'
      case 'auth/wrong-password':
        return 'Incorrect password'
      case 'auth/email-already-in-use':
        return 'An account with this email already exists'
      case 'auth/weak-password':
        return 'Password should be at least 6 characters'
      case 'auth/invalid-email':
        return 'Invalid email address'
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later'
      default:
        return 'An error occurred during authentication'
    }
  }
}
