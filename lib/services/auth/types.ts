import { User, UserRole, LoginCredentials, RegisterData } from '../../types'

export interface AuthAdapter {
  login(credentials: LoginCredentials): Promise<User>
  register(data: RegisterData): Promise<User>
  registerWithInvitation(data: RegisterData, invitationToken: string): Promise<User>
  loginWithGoogle(): Promise<User>
  logout(): Promise<void>
  getCurrentUser(): Promise<User | null>
  onAuthStateChange(callback: (user: User | null) => void): () => void
  updateUserRole(uid: string, role: UserRole): Promise<void>
}

export type AuthProvider = 'firebase' | 'auth0' | 'supabase'

export interface AuthConfig {
  provider: AuthProvider
  firebaseConfig?: any
  auth0Config?: any
  supabaseConfig?: any
}
