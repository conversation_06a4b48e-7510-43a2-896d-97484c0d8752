/**
 * API Route Authentication Wrappers
 * Higher-order functions to wrap API routes with authentication and authorization
 */

import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../database'
import { UserRole, User } from '../types'

export interface AuthenticatedApiRequest extends NextRequest {
  user: User
  decodedToken: any
}

/**
 * Standard API error response format
 */
export interface ApiErrorResponse {
  error: string
  code?: string
  details?: any
}

/**
 * Authentication result interface
 */
export interface AuthResult {
  success: boolean
  user?: User
  decodedToken?: any
  error?: string
  status?: number
}

export type ApiHandler = (request: NextRequest, context?: any) => Promise<NextResponse>
export type AuthenticatedApiHandler = (request: AuthenticatedApiRequest, context?: any) => Promise<NextResponse>



/**
 * Authenticate user from request headers
 * For now, this is a simplified implementation that expects the Firebase UID in the Authorization header
 * In a production environment, you would verify the Firebase ID token here
 */
async function authenticateFromHeaders(request: NextRequest): Promise<AuthResult> {
  try {
    // Get Authorization header
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.warn('API Authentication: No authorization header found in request')
      return {
        success: false,
        error: 'Authentication required. Please provide a valid authorization header.',
        status: 401
      }
    }

    // Extract the token (in this simplified version, it's the Firebase UID)
    // In production, this would be a Firebase ID token that needs verification
    const firebaseUid = authHeader.replace('Bearer ', '')

    if (!firebaseUid) {
      console.warn('API Authentication: Empty token in authorization header')
      return {
        success: false,
        error: 'Invalid authentication token.',
        status: 401
      }
    }

    // Get user from database
    const dataAdapter = getDatabaseAdapter()
    const user = await dataAdapter.getUserByFirebaseUid(firebaseUid)

    if (!user) {
      console.warn('API Authentication: User not found in database for Firebase UID:', firebaseUid)
      return {
        success: false,
        error: 'User account not found. Please contact support.',
        status: 404
      }
    }

    // Check if user account is active
    if (user.isActive === false) {
      console.warn('API Authentication: Inactive user attempted access:', user.uid)
      return {
        success: false,
        error: 'User account is inactive. Please contact support.',
        status: 403
      }
    }

    console.log('API Authentication: Successful authentication for user:', user.uid, 'role:', user.role)
    return {
      success: true,
      user,
      decodedToken: { uid: firebaseUid } // Simplified token structure
    }
  } catch (error) {
    console.error('API Authentication: Authentication error:', error)
    return {
      success: false,
      error: 'Authentication service temporarily unavailable. Please try again.',
      status: 500
    }
  }
}

/**
 * Check if user has required role
 */
function hasRequiredRole(user: User, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(user.role)
}

/**
 * Create standardized error response
 */
export function createErrorResponse(error: string, status: number, code?: string, details?: any): NextResponse {
  const response: ApiErrorResponse = { error }
  if (code) response.code = code
  if (details) response.details = details

  return NextResponse.json(response, { status })
}

/**
 * Log API access attempt
 */
function logApiAccess(request: NextRequest, user?: User, success: boolean = true, error?: string) {
  const method = request.method
  const url = request.url
  const userInfo = user ? `${user.uid} (${user.role})` : 'anonymous'

  if (success) {
    console.log(`API Access: ${method} ${url} - User: ${userInfo} - SUCCESS`)
  } else {
    console.warn(`API Access: ${method} ${url} - User: ${userInfo} - FAILED: ${error}`)
  }
}

/**
 * Create an authenticated API route wrapper
 */
export function withApiAuth(
  handler: AuthenticatedApiHandler,
  options: {
    allowedRoles?: UserRole[]
    requireAuth?: boolean
    logAccess?: boolean
  } = {}
) {
  const { allowedRoles = [], requireAuth = true, logAccess = true } = options

  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      // Skip authentication if not required
      if (!requireAuth) {
        if (logAccess) {
          logApiAccess(request, undefined, true, 'No auth required')
        }
        return handler(request as AuthenticatedApiRequest, context)
      }

      // Authenticate user
      const authResult = await authenticateFromHeaders(request)

      if (!authResult.success) {
        if (logAccess) {
          logApiAccess(request, undefined, false, authResult.error)
        }
        return createErrorResponse(
          authResult.error || 'Authentication failed',
          authResult.status || 401,
          'AUTH_FAILED'
        )
      }

      // Check role authorization
      if (allowedRoles.length > 0 && authResult.user) {
        if (!hasRequiredRole(authResult.user, allowedRoles)) {
          if (logAccess) {
            logApiAccess(request, authResult.user, false, `Role ${authResult.user.role} not in allowed roles: ${allowedRoles.join(', ')}`)
          }
          return createErrorResponse(
            `Access denied. Required roles: ${allowedRoles.join(', ')}`,
            403,
            'INSUFFICIENT_PERMISSIONS',
            { userRole: authResult.user.role, allowedRoles }
          )
        }
      }

      // Log successful access
      if (logAccess) {
        logApiAccess(request, authResult.user, true)
      }

      // Create authenticated request
      const authenticatedRequest = request as AuthenticatedApiRequest
      authenticatedRequest.user = authResult.user!
      authenticatedRequest.decodedToken = authResult.decodedToken

      return handler(authenticatedRequest, context)
    } catch (error) {
      console.error('API Auth Wrapper Error:', error)
      if (logAccess) {
        logApiAccess(request, undefined, false, 'Internal server error')
      }
      return createErrorResponse(
        'Internal server error',
        500,
        'INTERNAL_ERROR'
      )
    }
  }
}

/**
 * Pre-configured authentication wrappers for different roles
 */

// Admin only routes
export const withAdminAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin'] })

// Doctor and admin routes
export const withDoctorAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin', 'doctor'] })

// All authenticated users
export const withUserAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin', 'doctor', 'patient'] })

// Patient access (includes doctors and admins)
export const withPatientAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin', 'doctor', 'patient'] })

/**
 * Wrapper for routes that need patient-specific access control
 */
export function withPatientAccessControl(
  handler: (request: AuthenticatedApiRequest, patientId: string, context?: any) => Promise<NextResponse>
) {
  return withUserAuth(async (request: AuthenticatedApiRequest, context?: any) => {
    const user = request.user

    // Extract patient ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const patientIdIndex = pathSegments.findIndex(segment => segment === 'patients')
    
    if (patientIdIndex === -1 || patientIdIndex + 1 >= pathSegments.length) {
      return NextResponse.json(
        { error: 'Patient ID required in URL' },
        { status: 400 }
      )
    }

    const patientId = pathSegments[patientIdIndex + 1]

    // Check access permissions
    const hasAccess = await canAccessPatientData(user, patientId)
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied to this patient data' },
        { status: 403 }
      )
    }

    return handler(request, patientId, context)
  })
}

/**
 * Check if user can access specific patient data
 */
async function canAccessPatientData(user: User, patientId: string): Promise<boolean> {
  // Admin can access all patients
  if (user.role === 'admin') {
    return true
  }

  // Patient can only access their own data
  if (user.role === 'patient') {
    return user.uid === patientId
  }

  // Doctor can access assigned patients
  if (user.role === 'doctor') {
    // TODO: Implement doctor-patient assignment check
    // For now, allow all doctors to access all patients
    // In a real implementation, you would check the doctor-patient assignments
    return true
  }

  return false
}

/**
 * Wrapper for doctor-specific routes
 */
export function withDoctorAccessControl(
  handler: (request: AuthenticatedApiRequest, doctorId: string, context?: any) => Promise<NextResponse>
) {
  return withDoctorAuth(async (request: AuthenticatedApiRequest, context?: any) => {
    const user = request.user

    // Extract doctor ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const doctorIdIndex = pathSegments.findIndex(segment => segment === 'doctors')
    
    if (doctorIdIndex === -1 || doctorIdIndex + 1 >= pathSegments.length) {
      return NextResponse.json(
        { error: 'Doctor ID required in URL' },
        { status: 400 }
      )
    }

    const doctorId = pathSegments[doctorIdIndex + 1]

    // Check access permissions
    // Admins can access all doctors, doctors can only access their own data
    if (user.role === 'doctor' && user.uid !== doctorId) {
      return NextResponse.json(
        { error: 'Access denied to this doctor data' },
        { status: 403 }
      )
    }

    return handler(request, doctorId, context)
  })
}

/**
 * Utility function to get authenticated user from request
 */
export function getAuthenticatedUser(request: AuthenticatedApiRequest): User {
  return request.user
}

/**
 * Utility function to get decoded token from request
 */
export function getDecodedToken(request: AuthenticatedApiRequest): any {
  return request.decodedToken
}

/**
 * Check if user can perform admin actions
 */
export function isAdmin(user: User): boolean {
  return user.role === 'admin'
}

/**
 * Check if user can perform doctor actions
 */
export function isDoctor(user: User): boolean {
  return user.role === 'doctor' || user.role === 'admin'
}

/**
 * Check if user can perform patient actions
 */
export function isPatient(user: User): boolean {
  return user.role === 'patient' || user.role === 'doctor' || user.role === 'admin'
}

/**
 * Extract resource ID from URL path
 */
export function extractResourceId(request: NextRequest, resourceName: string): string | null {
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const resourceIndex = pathSegments.findIndex(segment => segment === resourceName)

  if (resourceIndex === -1 || resourceIndex + 1 >= pathSegments.length) {
    return null
  }

  return pathSegments[resourceIndex + 1]
}

/**
 * Validate UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

/**
 * Create a wrapper for resource-specific access control
 */
export function withResourceAccess(
  resourceName: string,
  accessCheck: (user: User, resourceId: string) => Promise<boolean> | boolean,
  handler: (request: AuthenticatedApiRequest, resourceId: string, context?: any) => Promise<NextResponse>
) {
  return withUserAuth(async (request: AuthenticatedApiRequest, context?: any) => {
    const user = request.user
    const resourceId = extractResourceId(request, resourceName)

    if (!resourceId) {
      return createErrorResponse(
        `${resourceName} ID required in URL`,
        400,
        'MISSING_RESOURCE_ID'
      )
    }

    if (!isValidUUID(resourceId)) {
      return createErrorResponse(
        `Invalid ${resourceName} ID format`,
        400,
        'INVALID_RESOURCE_ID'
      )
    }

    const hasAccess = await accessCheck(user, resourceId)
    if (!hasAccess) {
      return createErrorResponse(
        `Access denied to this ${resourceName}`,
        403,
        'RESOURCE_ACCESS_DENIED',
        { resourceName, resourceId, userRole: user.role }
      )
    }

    return handler(request, resourceId, context)
  })
}
