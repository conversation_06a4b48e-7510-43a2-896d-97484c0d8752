/**
 * API Route Authentication Wrappers
 * Higher-order functions to wrap API routes with authentication and authorization
 */

import { NextRequest, NextResponse } from 'next/server'
import { getTokens } from 'next-firebase-auth-edge'
import { getAuthConfigInstance } from '../config/auth'
import { getDatabaseAdapter } from '../database'
import { UserRole, User } from '../types'

export interface AuthenticatedApiRequest extends NextRequest {
  user: User
  decodedToken: any
}

export type ApiHandler = (request: NextRequest, context?: any) => Promise<NextResponse>
export type AuthenticatedApiHandler = (request: AuthenticatedApiRequest, context?: any) => Promise<NextResponse>

/**
 * Authentication result interface
 */
interface AuthResult {
  success: boolean
  user?: User
  decodedToken?: any
  error?: string
  status?: number
}

/**
 * Authenticate user from request cookies
 */
async function authenticateFromCookies(request: NextRequest): Promise<AuthResult> {
  try {
    const authConfig = getAuthConfigInstance()
    
    // Get tokens from cookies
    const tokens = await getTokens(request.cookies, {
      apiKey: authConfig.apiKey,
      cookieName: authConfig.cookieName,
      cookieSignatureKeys: authConfig.cookieSignatureKeys,
      serviceAccount: authConfig.serviceAccount,
      headers: request.headers
    })

    if (!tokens) {
      return {
        success: false,
        error: 'No authentication token found',
        status: 401
      }
    }

    // Get user from database
    const dataAdapter = getDatabaseAdapter()
    const user = await dataAdapter.getUserByFirebaseUid(tokens.decodedToken.uid)

    if (!user) {
      return {
        success: false,
        error: 'User not found in database',
        status: 404
      }
    }

    return {
      success: true,
      user,
      decodedToken: tokens.decodedToken
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed',
      status: 500
    }
  }
}

/**
 * Check if user has required role
 */
function hasRequiredRole(user: User, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(user.role)
}

/**
 * Create an authenticated API route wrapper
 */
export function withApiAuth(
  handler: AuthenticatedApiHandler,
  options: {
    allowedRoles?: UserRole[]
    requireAuth?: boolean
  } = {}
) {
  const { allowedRoles = [], requireAuth = true } = options

  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    // Skip authentication if not required
    if (!requireAuth) {
      return handler(request as AuthenticatedApiRequest, context)
    }

    // Authenticate user
    const authResult = await authenticateFromCookies(request)

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status || 401 }
      )
    }

    // Check role authorization
    if (allowedRoles.length > 0 && authResult.user) {
      if (!hasRequiredRole(authResult.user, allowedRoles)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }
    }

    // Create authenticated request
    const authenticatedRequest = request as AuthenticatedApiRequest
    authenticatedRequest.user = authResult.user!
    authenticatedRequest.decodedToken = authResult.decodedToken

    return handler(authenticatedRequest, context)
  }
}

/**
 * Pre-configured authentication wrappers for different roles
 */

// Admin only routes
export const withAdminAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin'] })

// Doctor and admin routes
export const withDoctorAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin', 'doctor'] })

// All authenticated users
export const withUserAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin', 'doctor', 'patient'] })

// Patient access (includes doctors and admins)
export const withPatientAuth = (handler: AuthenticatedApiHandler) =>
  withApiAuth(handler, { allowedRoles: ['admin', 'doctor', 'patient'] })

/**
 * Wrapper for routes that need patient-specific access control
 */
export function withPatientAccessControl(
  handler: (request: AuthenticatedApiRequest, patientId: string, context?: any) => Promise<NextResponse>
) {
  return withUserAuth(async (request: AuthenticatedApiRequest, context?: any) => {
    const user = request.user

    // Extract patient ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const patientIdIndex = pathSegments.findIndex(segment => segment === 'patients')
    
    if (patientIdIndex === -1 || patientIdIndex + 1 >= pathSegments.length) {
      return NextResponse.json(
        { error: 'Patient ID required in URL' },
        { status: 400 }
      )
    }

    const patientId = pathSegments[patientIdIndex + 1]

    // Check access permissions
    const hasAccess = await canAccessPatientData(user, patientId)
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied to this patient data' },
        { status: 403 }
      )
    }

    return handler(request, patientId, context)
  })
}

/**
 * Check if user can access specific patient data
 */
async function canAccessPatientData(user: User, patientId: string): Promise<boolean> {
  // Admin can access all patients
  if (user.role === 'admin') {
    return true
  }

  // Patient can only access their own data
  if (user.role === 'patient') {
    return user.uid === patientId
  }

  // Doctor can access assigned patients
  if (user.role === 'doctor') {
    // TODO: Implement doctor-patient assignment check
    // For now, allow all doctors to access all patients
    // In a real implementation, you would check the doctor-patient assignments
    return true
  }

  return false
}

/**
 * Wrapper for doctor-specific routes
 */
export function withDoctorAccessControl(
  handler: (request: AuthenticatedApiRequest, doctorId: string, context?: any) => Promise<NextResponse>
) {
  return withDoctorAuth(async (request: AuthenticatedApiRequest, context?: any) => {
    const user = request.user

    // Extract doctor ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const doctorIdIndex = pathSegments.findIndex(segment => segment === 'doctors')
    
    if (doctorIdIndex === -1 || doctorIdIndex + 1 >= pathSegments.length) {
      return NextResponse.json(
        { error: 'Doctor ID required in URL' },
        { status: 400 }
      )
    }

    const doctorId = pathSegments[doctorIdIndex + 1]

    // Check access permissions
    // Admins can access all doctors, doctors can only access their own data
    if (user.role === 'doctor' && user.uid !== doctorId) {
      return NextResponse.json(
        { error: 'Access denied to this doctor data' },
        { status: 403 }
      )
    }

    return handler(request, doctorId, context)
  })
}

/**
 * Utility function to get authenticated user from request
 */
export function getAuthenticatedUser(request: AuthenticatedApiRequest): User {
  return request.user
}

/**
 * Utility function to get decoded token from request
 */
export function getDecodedToken(request: AuthenticatedApiRequest): any {
  return request.decodedToken
}
