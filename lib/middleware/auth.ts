/**
 * Authentication middleware utilities for API routes
 * Provides functions to authenticate and authorize users in API endpoints
 */

import { NextRequest, NextResponse } from 'next/server'
import { getTokens } from 'next-firebase-auth-edge'
import { getAuthConfigInstance } from '../config/auth'
import { getDatabaseAdapter } from '../database'
import { UserRole, User } from '../types'

export interface AuthenticatedRequest extends NextRequest {
  user?: User
  decodedToken?: any
}

export interface AuthenticationResult {
  success: boolean
  user?: User
  error?: string
  status?: number
}

/**
 * Authenticate a user from the request cookies
 */
export async function authenticateUser(request: NextRequest): Promise<AuthenticationResult> {
  try {
    const authConfig = getAuthConfigInstance()
    
    // Get tokens from cookies
    const tokens = await getTokens(request.cookies, {
      apiKey: authConfig.apiKey,
      cookieName: authConfig.cookieName,
      cookieSignatureKeys: authConfig.cookieSignatureKeys,
      serviceAccount: authConfig.serviceAccount,
      headers: request.headers
    })

    if (!tokens) {
      return {
        success: false,
        error: 'No authentication token found',
        status: 401
      }
    }

    // Get user from database using Firebase UID
    const dataAdapter = getDatabaseAdapter()
    const user = await dataAdapter.getUserByFirebaseUid(tokens.decodedToken.uid)

    if (!user) {
      return {
        success: false,
        error: 'User not found in database',
        status: 404
      }
    }

    return {
      success: true,
      user
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed',
      status: 500
    }
  }
}

/**
 * Check if user has required role(s)
 */
export function hasRequiredRole(user: User, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(user.role)
}

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: {
    allowedRoles?: UserRole[]
    requireAuth?: boolean
  } = {}
) {
  const { allowedRoles = [], requireAuth = true } = options

  return async (request: NextRequest): Promise<NextResponse> => {
    // Skip authentication if not required
    if (!requireAuth) {
      return handler(request as AuthenticatedRequest)
    }

    // Authenticate user
    const authResult = await authenticateUser(request)

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status || 401 }
      )
    }

    // Check role authorization if roles are specified
    if (allowedRoles.length > 0 && authResult.user) {
      if (!hasRequiredRole(authResult.user, allowedRoles)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }
    }

    // Add user to request object
    const authenticatedRequest = request as AuthenticatedRequest
    authenticatedRequest.user = authResult.user

    return handler(authenticatedRequest)
  }
}

/**
 * Higher-order function to create role-specific middleware
 */
export function createRoleMiddleware(allowedRoles: UserRole[]) {
  return function (handler: (request: AuthenticatedRequest) => Promise<NextResponse>) {
    return withAuth(handler, { allowedRoles })
  }
}

/**
 * Pre-configured middleware for different roles
 */
export const withAdminAuth = createRoleMiddleware(['admin'])
export const withDoctorAuth = createRoleMiddleware(['admin', 'doctor'])
export const withPatientAuth = createRoleMiddleware(['admin', 'doctor', 'patient'])

/**
 * Middleware for admin-only routes
 */
export const adminOnly = createRoleMiddleware(['admin'])

/**
 * Middleware for doctor and admin routes
 */
export const doctorAndAdmin = createRoleMiddleware(['admin', 'doctor'])

/**
 * Middleware for all authenticated users
 */
export const authenticated = createRoleMiddleware(['admin', 'doctor', 'patient'])

/**
 * Extract user information from authenticated request
 */
export function getAuthenticatedUser(request: AuthenticatedRequest): User | null {
  return request.user || null
}

/**
 * Check if the authenticated user can access a specific patient's data
 * Patients can only access their own data, doctors can access assigned patients, admins can access all
 */
export async function canAccessPatient(user: User, patientId: string): Promise<boolean> {
  // Admin can access all patients
  if (user.role === 'admin') {
    return true
  }

  // Patient can only access their own data
  if (user.role === 'patient') {
    return user.uid === patientId
  }

  // Doctor can access assigned patients (this would need to be implemented based on your assignment logic)
  if (user.role === 'doctor') {
    // TODO: Implement doctor-patient assignment check
    // For now, allow all doctors to access all patients
    return true
  }

  return false
}

/**
 * Middleware for patient-specific routes with access control
 */
export function withPatientAccess(
  handler: (request: AuthenticatedRequest, patientId: string) => Promise<NextResponse>
) {
  return withAuth(async (request: AuthenticatedRequest) => {
    const user = getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Extract patient ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const patientId = pathSegments[pathSegments.indexOf('patients') + 1]

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID required' },
        { status: 400 }
      )
    }

    // Check access permissions
    const hasAccess = await canAccessPatient(user, patientId)
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied to this patient data' },
        { status: 403 }
      )
    }

    return handler(request, patientId)
  })
}
