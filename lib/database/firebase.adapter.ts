import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  QuerySnapshot,
  serverTimestamp,
  Timestamp,
} from 'firebase/firestore'
import { db } from '../config/firebase'
import { User, UserR<PERSON>, <PERSON><PERSON>, Doctor, Appointment, MedicalRecord, PaginationParams, PaginatedResponse } from '../types'
import { DataAdapter } from './types'

export class FirebaseDataAdapter implements DataAdapter {
  // User operations - Firebase doesn't handle user management, these are placeholders
  async createUser(userData: Omit<User, 'uid'> & { firebaseUid: string }): Promise<User> {
    throw new Error('User operations should be handled by Firebase Auth, not Firestore')
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | null> {
    throw new Error('User operations should be handled by Firebase Auth, not Firestore')
  }

  async getUserById(id: string): Promise<User | null> {
    throw new Error('User operations should be handled by Firebase Auth, not Firestore')
  }

  async updateUserRole(id: string, role: UserRole): Promise<void> {
    throw new Error('User operations should be handled by Firebase Auth, not Firestore')
  }

  // Doctor invitation operations - Not implemented for Firebase
  async createDoctorInvitation(email: string, invitedBy: string): Promise<{ token: string; expiresAt: Date }> {
    throw new Error('Doctor invitations not implemented for Firebase adapter')
  }

  async getDoctorInvitationByToken(token: string): Promise<any | null> {
    throw new Error('Doctor invitations not implemented for Firebase adapter')
  }

  async markInvitationAsUsed(token: string): Promise<void> {
    throw new Error('Doctor invitations not implemented for Firebase adapter')
  }

  // Patient operations
  async createPatient(patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient> {
    try {
      const docRef = await addDoc(collection(db, 'patients'), {
        ...patientData,
        dateOfBirth: Timestamp.fromDate(patientData.dateOfBirth),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return {
        id: docRef.id,
        ...patientData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    } catch (error) {
      throw new Error('Failed to create patient')
    }
  }

  async getPatientById(id: string): Promise<Patient | null> {
    try {
      const docSnap = await getDoc(doc(db, 'patients', id))
      
      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        dateOfBirth: data.dateOfBirth?.toDate() || new Date(),
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      } as Patient
    } catch (error) {
      throw new Error('Failed to fetch patient')
    }
  }

  async getPatientByUserId(userId: string): Promise<Patient | null> {
    try {
      const q = query(collection(db, 'patients'), where('userId', '==', userId))
      const querySnapshot = await getDocs(q)
      
      if (querySnapshot.empty) {
        return null
      }

      const doc = querySnapshot.docs[0]
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        dateOfBirth: data.dateOfBirth?.toDate() || new Date(),
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      } as Patient
    } catch (error) {
      throw new Error('Failed to fetch patient by user ID')
    }
  }

  async updatePatient(id: string, patientData: Partial<Patient>): Promise<Patient> {
    try {
      const updateData = { ...patientData }
      if (patientData.dateOfBirth) {
        updateData.dateOfBirth = Timestamp.fromDate(patientData.dateOfBirth) as any
      }
      updateData.updatedAt = serverTimestamp() as any

      await updateDoc(doc(db, 'patients', id), updateData)
      
      // Return updated patient
      const updatedPatient = await this.getPatientById(id)
      if (!updatedPatient) {
        throw new Error('Failed to retrieve updated patient')
      }
      return updatedPatient
    } catch (error) {
      throw new Error('Failed to update patient')
    }
  }

  async deletePatient(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'patients', id))
    } catch (error) {
      throw new Error('Failed to delete patient')
    }
  }

  async getPatients(params?: PaginationParams): Promise<PaginatedResponse<Patient>> {
    try {
      const paginationParams = {
        page: params?.page || 1,
        limit: params?.limit || 10,
        sortBy: params?.sortBy || 'createdAt',
        sortOrder: params?.sortOrder || 'desc' as const,
      }

      let q = query(
        collection(db, 'patients'),
        orderBy(paginationParams.sortBy, paginationParams.sortOrder),
        limit(paginationParams.limit)
      )

      const querySnapshot = await getDocs(q)
      const patients: Patient[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        patients.push({
          id: doc.id,
          ...data,
          dateOfBirth: data.dateOfBirth?.toDate() || new Date(),
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as Patient)
      })

      return {
        data: patients,
        pagination: {
          page: paginationParams.page,
          limit: paginationParams.limit,
          total: patients.length, // Firebase doesn't provide easy total count
          totalPages: Math.ceil(patients.length / paginationParams.limit),
          hasNext: paginationParams.page < Math.ceil(patients.length / paginationParams.limit),
          hasPrev: paginationParams.page > 1,
        },
      }
    } catch (error) {
      throw new Error('Failed to fetch patients')
    }
  }

  // Doctor operations - Placeholder implementations
  async createDoctor(doctorData: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Doctor> {
    throw new Error('Method not implemented')
  }

  async getDoctorById(id: string): Promise<Doctor | null> {
    throw new Error('Method not implemented')
  }

  async getDoctorByUserId(userId: string): Promise<Doctor | null> {
    throw new Error('Method not implemented')
  }

  async updateDoctor(id: string, doctorData: Partial<Doctor>): Promise<Doctor> {
    throw new Error('Method not implemented')
  }

  async deleteDoctor(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getDoctors(params?: PaginationParams): Promise<PaginatedResponse<Doctor>> {
    throw new Error('Method not implemented')
  }

  // Appointment operations - Placeholder implementations
  async createAppointment(appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Appointment> {
    throw new Error('Method not implemented')
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    throw new Error('Method not implemented')
  }

  async updateAppointment(id: string, appointmentData: Partial<Appointment>): Promise<Appointment> {
    throw new Error('Method not implemented')
  }

  async deleteAppointment(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getAppointments(params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async getAppointmentsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async getAppointmentsByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  // Medical Record operations - Placeholder implementations
  async createMedicalRecord(recordData: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<MedicalRecord> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecordById(id: string): Promise<MedicalRecord | null> {
    throw new Error('Method not implemented')
  }

  async updateMedicalRecord(id: string, recordData: Partial<MedicalRecord>): Promise<MedicalRecord> {
    throw new Error('Method not implemented')
  }

  async deleteMedicalRecord(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecords(params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecordsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>> {
    throw new Error('Method not implemented')
  }
}
