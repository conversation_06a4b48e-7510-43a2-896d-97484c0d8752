import { DataAdapter, DatabaseType, DatabaseConfig } from './types'
import { PostgresDataAdapter } from './postgres.adapter'
import { FirebaseDataAdapter } from './firebase.adapter'

/**
 * Factory function to create the appropriate database adapter based on environment configuration
 */
function createDatabaseAdapter(): DataAdapter {
  const databaseType = (process.env.DATABASE_TYPE || 'postgresql') as DatabaseType
  
  switch (databaseType) {
    case 'postgresql':
      return new PostgresDataAdapter()
    case 'firebase':
      return new FirebaseDataAdapter()
    default:
      console.warn(`Unknown database type: ${databaseType}. Falling back to PostgreSQL.`)
      return new PostgresDataAdapter()
  }
}

/**
 * Get database configuration from environment variables
 */
export function getDatabaseConfig(): DatabaseConfig {
  const databaseType = (process.env.DATABASE_TYPE || 'postgresql') as DatabaseType
  
  return {
    type: databaseType,
    connectionString: process.env.DATABASE_URL,
    firebaseConfig: databaseType === 'firebase' ? {
      // Firebase config would be loaded from environment if needed
      // For now, Firebase config is handled in lib/config/firebase.ts
    } : undefined,
  }
}

/**
 * Singleton instance of the database adapter
 * This ensures we use the same adapter instance throughout the application
 */
let databaseAdapterInstance: DataAdapter | null = null

/**
 * Get the database adapter instance
 * Creates a new instance if one doesn't exist
 */
export function getDatabaseAdapter(): DataAdapter {
  if (!databaseAdapterInstance) {
    databaseAdapterInstance = createDatabaseAdapter()
  }
  return databaseAdapterInstance
}

/**
 * Reset the database adapter instance (useful for testing)
 */
export function resetDatabaseAdapter(): void {
  databaseAdapterInstance = null
}

// Export types for use in other modules
export type { DataAdapter, DatabaseType, DatabaseConfig } from './types'

// Export adapter classes for direct use if needed
export { PostgresDataAdapter } from './postgres.adapter'
export { FirebaseDataAdapter } from './firebase.adapter'

// Default export is the adapter instance
export default getDatabaseAdapter()
