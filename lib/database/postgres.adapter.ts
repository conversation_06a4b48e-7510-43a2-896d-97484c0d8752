import { eq, and, desc, asc, gte, lte, like, count } from 'drizzle-orm'
import { db } from '../config/database'
import { users, doctorInvitations } from '../config/schema'
import { User, <PERSON>r<PERSON><PERSON>, <PERSON>ient, Doctor, Appointment, MedicalRecord, PaginationParams, PaginatedResponse } from '../types'
import crypto from 'crypto-js'
import { DataAdapter } from './types'

export class PostgresDataAdapter implements DataAdapter {
  // User operations
  async createUser(userData: Omit<User, 'uid'> & { firebaseUid: string }): Promise<User> {
    try {
      const [user] = await db.insert(users).values({
        firebaseUid: userData.firebaseUid,
        email: userData.email,
        displayName: userData.displayName || 'User',
        role: userData.role,
      }).returning()

      return {
        uid: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role as User<PERSON><PERSON>,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      console.error('Error creating user:', error)
      throw new Error('Failed to create user')
    }
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | null> {
    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.firebaseUid, firebaseUid))
        .limit(1)

      if (!user) return null

      return {
        uid: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role as UserRole,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      console.error('Error getting user by Firebase UID:', error)
      throw new Error('Failed to get user')
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1)

      if (!user) return null

      return {
        uid: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role as UserRole,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      console.error('Error getting user by ID:', error)
      throw new Error('Failed to get user')
    }
  }

  async updateUserRole(id: string, role: UserRole): Promise<void> {
    try {
      await db
        .update(users)
        .set({ 
          role,
          updatedAt: new Date()
        })
        .where(eq(users.id, id))
    } catch (error) {
      console.error('Error updating user role:', error)
      throw new Error('Failed to update user role')
    }
  }

  // Doctor invitation operations
  async createDoctorInvitation(email: string, invitedBy: string): Promise<{ token: string; expiresAt: Date }> {
    try {
      const token = crypto.lib.WordArray.random(32).toString()
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now

      await db.insert(doctorInvitations).values({
        email,
        token,
        invitedBy,
        expiresAt,
        isUsed: false,
      })

      return { token, expiresAt }
    } catch (error) {
      console.error('Error creating doctor invitation:', error)
      throw new Error('Failed to create doctor invitation')
    }
  }

  async getDoctorInvitationByToken(token: string): Promise<any | null> {
    try {
      const [invitation] = await db
        .select()
        .from(doctorInvitations)
        .where(eq(doctorInvitations.token, token))
        .limit(1)

      return invitation || null
    } catch (error) {
      console.error('Error getting doctor invitation by token:', error)
      throw new Error('Failed to get doctor invitation')
    }
  }

  async markInvitationAsUsed(token: string): Promise<void> {
    try {
      await db
        .update(doctorInvitations)
        .set({
          isUsed: true,
          usedAt: new Date()
        })
        .where(eq(doctorInvitations.token, token))
    } catch (error) {
      console.error('Error marking invitation as used:', error)
      throw new Error('Failed to mark invitation as used')
    }
  }

  // Patient operations - Placeholder implementation
  async createPatient(_patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient> {
    throw new Error('Method not implemented - Patient operations need schema alignment')
  }

  async getPatientById(_id: string): Promise<Patient | null> {
    throw new Error('Method not implemented - Patient operations need schema alignment')
  }

  async getPatientByUserId(_userId: string): Promise<Patient | null> {
    throw new Error('Method not implemented - Patient operations need schema alignment')
  }

  // Placeholder implementations for remaining methods
  async updatePatient(id: string, patientData: Partial<Patient>): Promise<Patient> {
    throw new Error('Method not implemented')
  }

  async deletePatient(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getPatients(params?: PaginationParams): Promise<PaginatedResponse<Patient>> {
    throw new Error('Method not implemented')
  }

  async createDoctor(doctorData: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Doctor> {
    throw new Error('Method not implemented')
  }

  async getDoctorById(id: string): Promise<Doctor | null> {
    throw new Error('Method not implemented')
  }

  async getDoctorByUserId(userId: string): Promise<Doctor | null> {
    throw new Error('Method not implemented')
  }

  async updateDoctor(id: string, doctorData: Partial<Doctor>): Promise<Doctor> {
    throw new Error('Method not implemented')
  }

  async deleteDoctor(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getDoctors(params?: PaginationParams): Promise<PaginatedResponse<Doctor>> {
    throw new Error('Method not implemented')
  }

  async createAppointment(appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Appointment> {
    throw new Error('Method not implemented')
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    throw new Error('Method not implemented')
  }

  async updateAppointment(id: string, appointmentData: Partial<Appointment>): Promise<Appointment> {
    throw new Error('Method not implemented')
  }

  async deleteAppointment(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getAppointments(params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async getAppointmentsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async getAppointmentsByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async createMedicalRecord(recordData: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<MedicalRecord> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecordById(id: string): Promise<MedicalRecord | null> {
    throw new Error('Method not implemented')
  }

  async updateMedicalRecord(id: string, recordData: Partial<MedicalRecord>): Promise<MedicalRecord> {
    throw new Error('Method not implemented')
  }

  async deleteMedicalRecord(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecords(params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecordsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>> {
    throw new Error('Method not implemented')
  }
}
