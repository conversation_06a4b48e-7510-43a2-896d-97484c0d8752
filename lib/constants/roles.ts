import { UserRole } from '../types'

export const USER_ROLES: Record<UserRole, string> = {
  admin: 'Administrator',
  doctor: 'Doctor',
  patient: 'Patient'
}

export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  admin: [
    'manage_users',
    'invite_doctors',
    'view_all_patients',
    'view_all_doctors',
    'manage_system_settings',
    'view_analytics'
  ],
  doctor: [
    'view_assigned_patients',
    'manage_patient_records',
    'schedule_appointments',
    'view_medical_history',
    'update_patient_info'
  ],
  patient: [
    'view_own_records',
    'view_appointments',
    'update_profile',
    'view_medical_history'
  ]
}

export const DASHBOARD_ROUTES: Record<UserRole, string> = {
  admin: '/dashboard/admin',
  doctor: '/dashboard/doctor',
  patient: '/dashboard/patient'
}

export const hasPermission = (userRole: UserRole, permission: string): boolean => {
  return ROLE_PERMISSIONS[userRole].includes(permission)
}
