import { getDatabaseAdapter, getDatabaseConfig, resetDatabaseAdapter } from '../../lib/database'
import { PostgresDataAdapter } from '../../lib/database/postgres.adapter'
import { FirebaseDataAdapter } from '../../lib/database/firebase.adapter'

// Mock the adapter classes
jest.mock('../../lib/database/postgres.adapter', () => ({
  PostgresDataAdapter: jest.fn().mockImplementation(() => ({
    createUser: jest.fn(),
    getUserByFirebaseUid: jest.fn(),
    // Add other methods as needed for testing
  })),
}))

jest.mock('../../lib/database/firebase.adapter', () => ({
  FirebaseDataAdapter: jest.fn().mockImplementation(() => ({
    createUser: jest.fn(),
    getUserByFirebaseUid: jest.fn(),
    // Add other methods as needed for testing
  })),
}))

const MockedPostgresDataAdapter = PostgresDataAdapter as jest.MockedClass<typeof PostgresDataAdapter>
const MockedFirebaseDataAdapter = FirebaseDataAdapter as jest.MockedClass<typeof FirebaseDataAdapter>

describe('Database Factory', () => {
  const originalEnv = process.env

  beforeEach(() => {
    jest.clearAllMocks()
    // Reset environment variables
    process.env = { ...originalEnv }
    // Reset the singleton instance
    resetDatabaseAdapter()
  })

  afterAll(() => {
    process.env = originalEnv
  })

  describe('getDatabaseAdapter', () => {
    it('should create PostgreSQL adapter by default', () => {
      delete process.env.DATABASE_TYPE

      const adapter = getDatabaseAdapter()

      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('createUser')
      expect(adapter).toHaveProperty('getUserByFirebaseUid')
    })

    it('should create PostgreSQL adapter when DATABASE_TYPE is postgresql', () => {
      process.env.DATABASE_TYPE = 'postgresql'

      const adapter = getDatabaseAdapter()

      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('createUser')
      expect(adapter).toHaveProperty('getUserByFirebaseUid')
    })

    it('should create Firebase adapter when DATABASE_TYPE is firebase', () => {
      process.env.DATABASE_TYPE = 'firebase'

      const adapter = getDatabaseAdapter()

      expect(MockedFirebaseDataAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('createUser')
      expect(adapter).toHaveProperty('getUserByFirebaseUid')
    })

    it('should fall back to PostgreSQL adapter for unknown database type', () => {
      process.env.DATABASE_TYPE = 'unknown'
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()

      const adapter = getDatabaseAdapter()

      expect(consoleSpy).toHaveBeenCalledWith('Unknown database type: unknown. Falling back to PostgreSQL.')
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('createUser')
      expect(adapter).toHaveProperty('getUserByFirebaseUid')

      consoleSpy.mockRestore()
    })

    it('should return the same instance on subsequent calls (singleton)', () => {
      process.env.DATABASE_TYPE = 'postgresql'
      
      const adapter1 = getDatabaseAdapter()
      const adapter2 = getDatabaseAdapter()
      
      expect(adapter1).toBe(adapter2)
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
    })

    it('should create new instance after reset', () => {
      process.env.DATABASE_TYPE = 'postgresql'
      
      const adapter1 = getDatabaseAdapter()
      resetDatabaseAdapter()
      const adapter2 = getDatabaseAdapter()
      
      expect(adapter1).not.toBe(adapter2)
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(2)
    })
  })

  describe('getDatabaseConfig', () => {
    it('should return PostgreSQL config by default', () => {
      delete process.env.DATABASE_TYPE
      delete process.env.DATABASE_URL
      
      const config = getDatabaseConfig()
      
      expect(config).toEqual({
        type: 'postgresql',
        connectionString: undefined,
        firebaseConfig: undefined,
      })
    })

    it('should return PostgreSQL config with connection string', () => {
      process.env.DATABASE_TYPE = 'postgresql'
      process.env.DATABASE_URL = 'postgresql://localhost:5432/test'
      
      const config = getDatabaseConfig()
      
      expect(config).toEqual({
        type: 'postgresql',
        connectionString: 'postgresql://localhost:5432/test',
        firebaseConfig: undefined,
      })
    })

    it('should return Firebase config', () => {
      process.env.DATABASE_TYPE = 'firebase'
      process.env.DATABASE_URL = 'some-url'
      
      const config = getDatabaseConfig()
      
      expect(config).toEqual({
        type: 'firebase',
        connectionString: 'some-url',
        firebaseConfig: {
          // Firebase config would be loaded from environment if needed
          // For now, Firebase config is handled in lib/config/firebase.ts
        },
      })
    })

    it('should handle missing environment variables gracefully', () => {
      delete process.env.DATABASE_TYPE
      delete process.env.DATABASE_URL
      
      const config = getDatabaseConfig()
      
      expect(config.type).toBe('postgresql')
      expect(config.connectionString).toBeUndefined()
      expect(config.firebaseConfig).toBeUndefined()
    })
  })

  describe('resetDatabaseAdapter', () => {
    it('should reset the singleton instance', () => {
      process.env.DATABASE_TYPE = 'postgresql'
      
      // Create first instance
      const adapter1 = getDatabaseAdapter()
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
      
      // Reset and create second instance
      resetDatabaseAdapter()
      const adapter2 = getDatabaseAdapter()
      
      expect(adapter1).not.toBe(adapter2)
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(2)
    })

    it('should allow switching adapter types after reset', () => {
      // Start with PostgreSQL
      process.env.DATABASE_TYPE = 'postgresql'
      const postgresAdapter = getDatabaseAdapter()
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
      expect(MockedFirebaseDataAdapter).toHaveBeenCalledTimes(0)
      
      // Reset and switch to Firebase
      resetDatabaseAdapter()
      process.env.DATABASE_TYPE = 'firebase'
      const firebaseAdapter = getDatabaseAdapter()
      
      expect(postgresAdapter).not.toBe(firebaseAdapter)
      expect(MockedPostgresDataAdapter).toHaveBeenCalledTimes(1)
      expect(MockedFirebaseDataAdapter).toHaveBeenCalledTimes(1)
    })
  })

  describe('Environment-driven Selection', () => {
    it('should respect DATABASE_TYPE environment variable changes', () => {
      // Test PostgreSQL
      process.env.DATABASE_TYPE = 'postgresql'
      resetDatabaseAdapter()
      const postgresAdapter = getDatabaseAdapter()
      expect(postgresAdapter).toHaveProperty('createUser')

      // Test Firebase
      process.env.DATABASE_TYPE = 'firebase'
      resetDatabaseAdapter()
      const firebaseAdapter = getDatabaseAdapter()
      expect(firebaseAdapter).toHaveProperty('createUser')

      // Test fallback
      process.env.DATABASE_TYPE = 'invalid'
      resetDatabaseAdapter()
      const fallbackAdapter = getDatabaseAdapter()
      expect(fallbackAdapter).toHaveProperty('createUser')
    })

    it('should handle case sensitivity in database type', () => {
      process.env.DATABASE_TYPE = 'POSTGRESQL'
      resetDatabaseAdapter()
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      const adapter = getDatabaseAdapter()
      
      expect(consoleSpy).toHaveBeenCalledWith('Unknown database type: POSTGRESQL. Falling back to PostgreSQL.')
      expect(adapter).toHaveProperty('createUser')
      expect(adapter).toHaveProperty('getUserByFirebaseUid')
      
      consoleSpy.mockRestore()
    })
  })

  describe('Error Handling', () => {
    it('should handle adapter instantiation errors gracefully', () => {
      MockedPostgresDataAdapter.mockImplementationOnce(() => {
        throw new Error('Adapter creation failed')
      })
      
      process.env.DATABASE_TYPE = 'postgresql'
      resetDatabaseAdapter()
      
      expect(() => getDatabaseAdapter()).toThrow('Adapter creation failed')
    })
  })
})
