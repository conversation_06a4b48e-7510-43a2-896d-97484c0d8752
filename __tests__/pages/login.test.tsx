import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import LoginPage from '../../app/login/page'
import { useAuth } from '../../lib/hooks'

// Mock useAuth hook
jest.mock('../../lib/hooks', () => ({
  useAuth: jest.fn(),
}))

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

const mockPush = jest.fn()
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('Login Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    })
  })

  it('should render login form when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    expect(screen.getByText('Welcome Back')).toBeInTheDocument()
    expect(screen.getByText('Sign in to your Patient Management System account')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
  })

  it('should show loading state when auth is loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: true,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByText('Welcome Back')).not.toBeInTheDocument()
  })

  it('should redirect authenticated user to dashboard', () => {
    const mockUser = {
      uid: '123',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: 'admin' as const,
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    expect(mockPush).toHaveBeenCalledWith('/dashboard/admin')
  })

  it('should handle form submission', async () => {
    const mockLogin = jest.fn().mockResolvedValue(undefined)
    const mockClearError = jest.fn()

    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: null,
      login: mockLogin,
      register: jest.fn(),
      logout: jest.fn(),
      clearError: mockClearError,
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: 'Sign In' })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })

  it('should display validation errors for empty fields', async () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    // Remove required attributes to allow form submission
    const form = screen.getByRole('button', { name: 'Sign In' }).closest('form')!
    const inputs = form.querySelectorAll('input[required]')
    inputs.forEach(input => input.removeAttribute('required'))

    const submitButton = screen.getByRole('button', { name: 'Sign In' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument()
      expect(screen.getByText('Password is required')).toBeInTheDocument()
    })
  })

  it('should display authentication error', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: 'Invalid email or password',
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    expect(screen.getByText('Invalid email or password')).toBeInTheDocument()
  })

  it('should show demo accounts information', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(<LoginPage />)

    expect(screen.getByText('Demo Accounts:')).toBeInTheDocument()
    expect(screen.getByText('Admin:')).toBeInTheDocument()
    expect(screen.getByText('Doctor:')).toBeInTheDocument()
    expect(screen.getByText('Patient:')).toBeInTheDocument()
  })
})
