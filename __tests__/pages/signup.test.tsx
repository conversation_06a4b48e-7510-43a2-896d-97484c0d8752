import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useRouter, useSearchParams } from 'next/navigation'
import SignupPage from '../../app/signup/page'
import { useAuth } from '../../lib/hooks'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Mock auth hook
jest.mock('../../lib/hooks', () => ({
  useAuth: jest.fn(),
}))

const mockPush = jest.fn()
const mockRegister = jest.fn()
const mockLoginWithGoogle = jest.fn()

beforeEach(() => {
  jest.clearAllMocks()
  ;(useRouter as jest.Mock).mockReturnValue({
    push: mockPush,
  })
  ;(useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams())
  ;(useAuth as jest.Mock).mockReturnValue({
    register: mockRegister,
    loginWithGoogle: mockLoginWithGoogle,
  })
})

describe('Signup Page', () => {
  it('should render signup form', () => {
    render(<SignupPage />)

    expect(screen.getByText('Create Account')).toBeInTheDocument()
    expect(screen.getByText('Join the Patient Management System as a patient')).toBeInTheDocument()
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument()
    expect(screen.getByText(/Account Type:/)).toBeInTheDocument()
    expect(screen.getByText('Patient')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Continue with Google' })).toBeInTheDocument()
  })

  it('should display validation errors for empty fields', async () => {
    render(<SignupPage />)

    // Remove required attributes to allow form submission
    const form = screen.getByRole('button', { name: 'Create Account' }).closest('form')!
    const inputs = form.querySelectorAll('input[required]')
    inputs.forEach(input => input.removeAttribute('required'))

    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Display name is required')).toBeInTheDocument()
      expect(screen.getByText('Email is required')).toBeInTheDocument()
      expect(screen.getByText('Password is required')).toBeInTheDocument()
      expect(screen.getByText('Please confirm your password')).toBeInTheDocument()
      expect(screen.getByText('Display name is required')).toBeInTheDocument()
    })
  })

  it('should display validation error for invalid email', async () => {
    render(<SignupPage />)

    // Remove required attributes first to allow form submission
    const form = screen.getByRole('button', { name: 'Create Account' }).closest('form')!
    const inputs = form.querySelectorAll('input[required]')
    inputs.forEach(input => input.removeAttribute('required'))

    // Fill all fields but make email invalid
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    const emailInput = screen.getByLabelText('Email')
    fireEvent.change(emailInput, { target: { value: 'invalid-email@domain' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } })

    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Email is invalid')).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('should display validation error for short password', async () => {
    render(<SignupPage />)

    // Remove required attributes to allow form submission
    const form = screen.getByRole('button', { name: 'Create Account' }).closest('form')!
    const inputs = form.querySelectorAll('input[required]')
    inputs.forEach(input => input.removeAttribute('required'))

    // Fill all fields but make password too short
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    const passwordInput = screen.getByLabelText('Password')
    fireEvent.change(passwordInput, { target: { value: '123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: '123' } })

    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument()
    })
  })

  it('should display validation error for password mismatch', async () => {
    render(<SignupPage />)

    // Remove required attributes to allow form submission
    const form = screen.getByRole('button', { name: 'Create Account' }).closest('form')!
    const inputs = form.querySelectorAll('input[required]')
    inputs.forEach(input => input.removeAttribute('required'))

    // Fill all fields but make passwords not match
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    const passwordInput = screen.getByLabelText('Password')
    const confirmPasswordInput = screen.getByLabelText('Confirm Password')

    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'different123' } })

    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Passwords do not match')).toBeInTheDocument()
    })
  })

  it('should call register with correct data on form submission', async () => {
    mockRegister.mockResolvedValue({})

    render(<SignupPage />)

    // Fill out the form
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } })

    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'John Doe',
        role: 'patient'
      })
    })
  })

  it('should redirect to correct dashboard after successful registration', async () => {
    mockRegister.mockResolvedValue({})

    render(<SignupPage />)

    // Fill out the form with admin role
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'Patient User' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } })

    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard/patient')
    })
  })

  it('should handle Google signup', async () => {
    mockLoginWithGoogle.mockResolvedValue({})
    
    render(<SignupPage />)
    
    const googleButton = screen.getByRole('button', { name: 'Continue with Google' })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(mockLoginWithGoogle).toHaveBeenCalled()
      expect(mockPush).toHaveBeenCalledWith('/dashboard/patient')
    })
  })

  it('should display error message on registration failure', async () => {
    const errorMessage = 'Registration failed'
    mockRegister.mockRejectedValue(new Error(errorMessage))
    
    render(<SignupPage />)
    
    // Fill out the form
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } })
    
    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('should clear errors when user starts typing', async () => {
    render(<SignupPage />)

    // Remove required attributes to allow form submission
    const form = screen.getByRole('button', { name: 'Create Account' }).closest('form')!
    const inputs = form.querySelectorAll('input[required]')
    inputs.forEach(input => input.removeAttribute('required'))

    // Trigger validation error
    const submitButton = screen.getByRole('button', { name: 'Create Account' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument()
    })

    // Start typing in email field
    const emailInput = screen.getByLabelText('Email')
    fireEvent.change(emailInput, { target: { value: 'j' } })

    await waitFor(() => {
      expect(screen.queryByText('Email is required')).not.toBeInTheDocument()
    })
  })

  it('should have link to login page', () => {
    render(<SignupPage />)
    
    const loginLink = screen.getByRole('link', { name: 'Sign in' })
    expect(loginLink).toBeInTheDocument()
    expect(loginLink).toHaveAttribute('href', '/login')
  })
})
