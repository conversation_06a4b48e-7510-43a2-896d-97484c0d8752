import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import PatientDashboard from '../../app/dashboard/patient/page'

// Mock ProtectedRoute component
jest.mock('../../components/auth', () => ({
  ProtectedRoute: ({ children, allowedRoles }: any) => (
    <div data-testid="protected-route" data-allowed-roles={allowedRoles?.join(',')}>
      {children}
    </div>
  ),
}))

// Mock UI components
jest.mock('../../components/ui', () => ({
  Card: ({ children, className, ...props }: any) => (
    <div data-testid="card" className={className} {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, className, ...props }: any) => (
    <div data-testid="card-content" className={className} {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, className, ...props }: any) => (
    <div data-testid="card-description" className={className} {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children, className, ...props }: any) => (
    <div data-testid="card-header" className={className} {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, className, ...props }: any) => (
    <div data-testid="card-title" className={className} {...props}>
      {children}
    </div>
  ),
  Button: ({ children, variant, size, className, ...props }: any) => (
    <button data-variant={variant} data-size={size} className={className} {...props}>
      {children}
    </button>
  ),
  Badge: ({ children, variant, className, ...props }: any) => (
    <span data-testid="badge" data-variant={variant} className={className} {...props}>
      {children}
    </span>
  ),
}))

// Mock icons
jest.mock('lucide-react', () => ({
  Calendar: (props: any) => <div data-testid="calendar-icon" {...props} />,
  FileText: (props: any) => <div data-testid="file-text-icon" {...props} />,
  User: (props: any) => <div data-testid="user-icon" {...props} />,
  Heart: (props: any) => <div data-testid="heart-icon" {...props} />,
  Clock: (props: any) => <div data-testid="clock-icon" {...props} />,
  MapPin: (props: any) => <div data-testid="map-pin-icon" {...props} />,
  Phone: (props: any) => <div data-testid="phone-icon" {...props} />,
}))

// Mock utils
jest.mock('../../lib/utils', () => ({
  formatDate: (date: Date) => date.toLocaleDateString(),
  formatDateTime: (date: Date) => date.toLocaleString(),
}))

describe('Patient Dashboard Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Component Structure', () => {
    it('should render within ProtectedRoute with patient role', () => {
      render(<PatientDashboard />)

      const protectedRoute = screen.getByTestId('protected-route')
      expect(protectedRoute).toBeInTheDocument()
      expect(protectedRoute).toHaveAttribute('data-allowed-roles', 'patient')
    })

    it('should render main dashboard header', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('My Health Dashboard')).toBeInTheDocument()
      expect(screen.getByText('View your medical information and manage appointments')).toBeInTheDocument()
    })

    it('should render header action buttons', () => {
      render(<PatientDashboard />)

      // Use getAllByText for text that appears multiple times
      const bookAppointmentButtons = screen.getAllByText('Book Appointment')
      expect(bookAppointmentButtons.length).toBeGreaterThan(0)

      const updateProfileButtons = screen.getAllByText('Update Profile')
      expect(updateProfileButtons.length).toBeGreaterThan(0)

      // Check that header buttons are clickable
      const headerBookButton = bookAppointmentButtons[0].closest('button')
      const headerUpdateButton = updateProfileButtons[0].closest('button')
      expect(headerBookButton).toBeInTheDocument()
      expect(headerUpdateButton).toBeInTheDocument()
      expect(headerBookButton).toHaveAttribute('data-variant', 'outline')
    })
  })

  describe('Personal Information Section', () => {
    it('should render personal information card', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Personal Information')).toBeInTheDocument()
      expect(screen.getByText('Your basic information and emergency contacts')).toBeInTheDocument()
    })

    it('should render basic information with correct data', () => {
      render(<PatientDashboard />)

      // Check for actual patient data from mockPatientInfo
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Born 5/15/1978')).toBeInTheDocument()
      expect(screen.getByText('+****************')).toBeInTheDocument()
      expect(screen.getByText('123 Main St, Anytown, ST 12345')).toBeInTheDocument()
    })

    it('should render emergency contact information', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Emergency Contact')).toBeInTheDocument()
      expect(screen.getByText('Jane Doe')).toBeInTheDocument()
      expect(screen.getByText('Spouse')).toBeInTheDocument()
      expect(screen.getByText('+****************')).toBeInTheDocument()
    })

    it('should render assigned doctor information', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Assigned Doctor')).toBeInTheDocument()

      // Use getAllByText for doctor name that appears in multiple sections
      const drSarahWilsonElements = screen.getAllByText('Dr. Sarah Wilson')
      expect(drSarahWilsonElements.length).toBeGreaterThan(0)

      expect(screen.getByText('Internal Medicine')).toBeInTheDocument()

      const contactDoctorButtons = screen.getAllByText('Contact Doctor')
      expect(contactDoctorButtons.length).toBeGreaterThan(0)
    })

    it('should render personal information icons correctly', () => {
      render(<PatientDashboard />)

      // Use getAllByTestId for icons that appear multiple times
      const userIcons = screen.getAllByTestId('user-icon')
      const calendarIcons = screen.getAllByTestId('calendar-icon')
      const phoneIcons = screen.getAllByTestId('phone-icon')
      const mapPinIcons = screen.getAllByTestId('map-pin-icon')
      const heartIcons = screen.getAllByTestId('heart-icon')

      expect(userIcons.length).toBeGreaterThan(0)
      expect(calendarIcons.length).toBeGreaterThan(0)
      expect(phoneIcons.length).toBeGreaterThan(0)
      expect(mapPinIcons.length).toBeGreaterThan(0)
      expect(heartIcons.length).toBeGreaterThan(0)
    })
  })

  describe('Vital Signs Section', () => {
    it('should render vital signs card', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Latest Vital Signs')).toBeInTheDocument()
      expect(screen.getByText('Last updated: 1/10/2024')).toBeInTheDocument()
    })

    it('should render all vital signs with correct data', () => {
      render(<PatientDashboard />)

      // Check for actual vital signs data from mockVitalSigns
      expect(screen.getByText('Blood Pressure')).toBeInTheDocument()
      expect(screen.getByText('120/80')).toBeInTheDocument()
      
      expect(screen.getByText('Heart Rate')).toBeInTheDocument()
      expect(screen.getByText('72 bpm')).toBeInTheDocument()
      
      expect(screen.getByText('Temperature')).toBeInTheDocument()
      expect(screen.getByText('98.6°F')).toBeInTheDocument()
      
      expect(screen.getByText('Weight')).toBeInTheDocument()
      expect(screen.getByText('175 lbs')).toBeInTheDocument()
      
      expect(screen.getByText('Height')).toBeInTheDocument()
      expect(screen.getByText('5\'10"')).toBeInTheDocument()
    })
  })

  describe('Upcoming Appointments Section', () => {
    it('should render upcoming appointments card', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Upcoming Appointments')).toBeInTheDocument()
      expect(screen.getByText('Your scheduled appointments')).toBeInTheDocument()
    })

    it('should render appointment list with correct data', () => {
      render(<PatientDashboard />)

      // Check for actual appointments from mockUpcomingAppointments
      // Use getAllByText for doctor names that appear in multiple sections
      const drSarahWilsonElements = screen.getAllByText('Dr. Sarah Wilson')
      expect(drSarahWilsonElements.length).toBeGreaterThan(0)

      expect(screen.getByText('1/20/2024 at 10:00 AM')).toBeInTheDocument()
      expect(screen.getByText('Room 201')).toBeInTheDocument()

      expect(screen.getByText('Dr. Michael Brown')).toBeInTheDocument()
      expect(screen.getByText('1/25/2024 at 02:30 PM')).toBeInTheDocument()
      expect(screen.getByText('Room 105')).toBeInTheDocument()
    })

    it('should render appointment badges for type and status', () => {
      render(<PatientDashboard />)

      // Check for appointment type badges
      expect(screen.getByText('routine')).toBeInTheDocument()
      expect(screen.getByText('follow-up')).toBeInTheDocument()

      // Check for status badges
      const confirmedBadges = screen.getAllByText('confirmed')
      expect(confirmedBadges.length).toBe(2) // Both appointments are confirmed
    })

    it('should render view all appointments button', () => {
      render(<PatientDashboard />)

      const viewAllButton = screen.getByText('View All Appointments')
      expect(viewAllButton).toBeInTheDocument()
      expect(viewAllButton.closest('button')).toHaveAttribute('data-variant', 'outline')
    })
  })

  describe('Recent Medical Records Section', () => {
    it('should render recent medical records card', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Recent Medical Records')).toBeInTheDocument()
      expect(screen.getByText('Your latest medical visits and treatments')).toBeInTheDocument()
    })

    it('should render medical records with correct data', () => {
      render(<PatientDashboard />)

      // Check for actual records from mockRecentRecords
      expect(screen.getByText('Annual Physical Exam')).toBeInTheDocument()
      expect(screen.getByText('1/10/2024 • Dr. Sarah Wilson')).toBeInTheDocument()
      expect(screen.getByText('Patient in good health. Blood pressure normal. Continue current medications.')).toBeInTheDocument()
      
      expect(screen.getByText('Hypertension Follow-up')).toBeInTheDocument()
      expect(screen.getByText('1/5/2024 • Dr. Sarah Wilson')).toBeInTheDocument()
      expect(screen.getByText('Blood pressure well controlled. Patient reports no side effects.')).toBeInTheDocument()
      
      expect(screen.getByText('Diabetes Management')).toBeInTheDocument()
      expect(screen.getByText('12/15/2023 • Dr. Michael Brown')).toBeInTheDocument()
      expect(screen.getByText('HbA1c levels improved. Continue current treatment plan.')).toBeInTheDocument()
    })

    it('should render medication badges', () => {
      render(<PatientDashboard />)

      // Check for medication badges from records - use getAllByText for medications that appear multiple times
      const lisinoprilElements = screen.getAllByText('Lisinopril 10mg')
      expect(lisinoprilElements.length).toBeGreaterThan(0)

      const metforminElements = screen.getAllByText('Metformin 500mg')
      expect(metforminElements.length).toBeGreaterThan(0)
    })

    it('should render view all records button', () => {
      render(<PatientDashboard />)

      const viewAllButton = screen.getByText('View All Records')
      expect(viewAllButton).toBeInTheDocument()
      expect(viewAllButton.closest('button')).toHaveAttribute('data-variant', 'outline')
    })
  })

  describe('Quick Actions Section', () => {
    it('should render quick actions card', () => {
      render(<PatientDashboard />)

      expect(screen.getByText('Quick Actions')).toBeInTheDocument()
      expect(screen.getByText('Common tasks and information')).toBeInTheDocument()
    })

    it('should render all quick action buttons', () => {
      render(<PatientDashboard />)

      // Check for actual quick action buttons from component - use getAllByText for duplicates
      const bookAppointmentButtons = screen.getAllByText('Book Appointment')
      expect(bookAppointmentButtons.length).toBeGreaterThan(0)
      
      expect(screen.getByText('View Records')).toBeInTheDocument()
      
      const updateProfileButtons = screen.getAllByText('Update Profile')
      expect(updateProfileButtons.length).toBeGreaterThan(0)
      
      const contactDoctorButtons = screen.getAllByText('Contact Doctor')
      expect(contactDoctorButtons.length).toBeGreaterThan(0)
    })

    it('should render quick action icons', () => {
      render(<PatientDashboard />)

      // Icons should be present (multiple instances due to header and quick actions)
      const calendarIcons = screen.getAllByTestId('calendar-icon')
      const fileTextIcons = screen.getAllByTestId('file-text-icon')
      const userIcons = screen.getAllByTestId('user-icon')
      const phoneIcons = screen.getAllByTestId('phone-icon')

      expect(calendarIcons.length).toBeGreaterThan(0)
      expect(fileTextIcons.length).toBeGreaterThan(0)
      expect(userIcons.length).toBeGreaterThan(0)
      expect(phoneIcons.length).toBeGreaterThan(0)
    })

    it('should handle quick action button clicks', () => {
      render(<PatientDashboard />)

      // Test clicking on quick action buttons
      const viewRecordsButton = screen.getByText('View Records')
      
      expect(viewRecordsButton.closest('button')).toBeInTheDocument()

      // Should be clickable
      fireEvent.click(viewRecordsButton)
      // In a real app, this would trigger navigation or modal
    })
  })

  describe('Responsive Layout', () => {
    it('should apply correct CSS classes for responsive design', () => {
      render(<PatientDashboard />)

      // Check for responsive grid classes - need to find the actual grid containers
      const personalInfoGrid = screen.getByText('Basic Information').closest('div')?.parentElement
      expect(personalInfoGrid).toHaveClass('grid', 'gap-4', 'md:grid-cols-2')

      const mainContentGrid = screen.getByText('Personal Information').closest('[data-testid="card"]')?.parentElement
      expect(mainContentGrid).toHaveClass('grid', 'gap-6', 'md:grid-cols-3')

      const appointmentsGrid = screen.getByText('Upcoming Appointments').closest('[data-testid="card"]')?.parentElement
      expect(appointmentsGrid).toHaveClass('grid', 'gap-6', 'md:grid-cols-2')

      // For quick actions, find the grid container that contains the buttons
      const quickActionsContainer = screen.getByText('View Records').closest('button')?.parentElement
      expect(quickActionsContainer).toHaveClass('grid', 'gap-4', 'md:grid-cols-4')
    })
  })

  describe('Data Display', () => {
    it('should display formatted dates correctly', () => {
      render(<PatientDashboard />)

      // Dates should be formatted using the mocked formatDate function
      expect(screen.getByText('Born 5/15/1978')).toBeInTheDocument()
      expect(screen.getByText('Last updated: 1/10/2024')).toBeInTheDocument()
      expect(screen.getByText('1/20/2024 at 10:00 AM')).toBeInTheDocument()
      expect(screen.getByText('1/25/2024 at 02:30 PM')).toBeInTheDocument()
    })

    it('should display patient contact information correctly', () => {
      render(<PatientDashboard />)

      // Contact information should be displayed correctly
      expect(screen.getByText('+****************')).toBeInTheDocument()
      expect(screen.getByText('+****************')).toBeInTheDocument()
      expect(screen.getByText('123 Main St, Anytown, ST 12345')).toBeInTheDocument()
    })
  })
})
