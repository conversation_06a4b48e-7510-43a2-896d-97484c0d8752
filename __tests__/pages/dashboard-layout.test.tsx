import React from 'react'
import { render, screen } from '@testing-library/react'
import DashboardLayout from '../../app/dashboard/layout'

// Mock ProtectedRoute component
jest.mock('../../components/auth', () => ({
  ProtectedRoute: ({ children }: any) => (
    <div data-testid="protected-route">
      {children}
    </div>
  ),
}))

// Mock ErrorBoundary component
jest.mock('../../components/error-boundary', () => {
  return function MockErrorBoundary({ children }: any) {
    return (
      <div data-testid="error-boundary">
        {children}
      </div>
    )
  }
})

// Mock Sidebar component
jest.mock('../../components/navigation/sidebar', () => ({
  Sidebar: () => (
    <div data-testid="sidebar">
      <div>Sidebar Content</div>
    </div>
  ),
}))

describe('Dashboard Layout', () => {
  describe('Component Structure', () => {
    it('should render ProtectedRoute as the outermost wrapper', () => {
      render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      expect(screen.getByTestId('protected-route')).toBeInTheDocument()
    })

    it('should render ErrorBoundary inside ProtectedRoute', () => {
      render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      const protectedRoute = screen.getByTestId('protected-route')
      const errorBoundary = screen.getByTestId('error-boundary')
      
      expect(protectedRoute).toContainElement(errorBoundary)
    })

    it('should render Sidebar component', () => {
      render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      expect(screen.getByText('Sidebar Content')).toBeInTheDocument()
    })

    it('should render children content in main area', () => {
      render(
        <DashboardLayout>
          <div data-testid="child-content">Dashboard Page Content</div>
        </DashboardLayout>
      )

      expect(screen.getByTestId('child-content')).toBeInTheDocument()
      expect(screen.getByText('Dashboard Page Content')).toBeInTheDocument()
    })
  })

  describe('Layout Structure', () => {
    it('should apply correct CSS classes for layout structure', () => {
      const { container } = render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      // Find the main layout container
      const layoutContainer = container.querySelector('.flex.h-screen.bg-background')
      expect(layoutContainer).toBeInTheDocument()

      // Find the sidebar container
      const sidebarContainer = container.querySelector('.w-64.flex-shrink-0')
      expect(sidebarContainer).toBeInTheDocument()

      // Find the main content container
      const mainContainer = container.querySelector('.flex-1.flex.flex-col.overflow-hidden')
      expect(mainContainer).toBeInTheDocument()

      // Find the main content area
      const mainContent = container.querySelector('main.flex-1.overflow-y-auto.p-6')
      expect(mainContent).toBeInTheDocument()
    })

    it('should have proper responsive design classes', () => {
      const { container } = render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      // Check for responsive classes
      const layoutContainer = container.querySelector('.flex.h-screen')
      expect(layoutContainer).toHaveClass('bg-background')

      const sidebarContainer = container.querySelector('.w-64')
      expect(sidebarContainer).toHaveClass('flex-shrink-0')

      const mainContainer = container.querySelector('.flex-1.flex.flex-col')
      expect(mainContainer).toHaveClass('overflow-hidden')

      const mainContent = container.querySelector('main')
      expect(mainContent).toHaveClass('flex-1', 'overflow-y-auto', 'p-6')
    })
  })

  describe('Component Integration', () => {
    it('should integrate all components in correct hierarchy', () => {
      render(
        <DashboardLayout>
          <div data-testid="test-child">Test Child</div>
        </DashboardLayout>
      )

      // Check hierarchy: ProtectedRoute > ErrorBoundary > Layout > Sidebar + Main
      const protectedRoute = screen.getByTestId('protected-route')
      const errorBoundary = screen.getByTestId('error-boundary')
      const sidebar = screen.getByTestId('sidebar')
      const testChild = screen.getByTestId('test-child')

      expect(protectedRoute).toContainElement(errorBoundary)
      expect(errorBoundary).toContainElement(sidebar)
      expect(errorBoundary).toContainElement(testChild)
    })

    it('should render multiple children correctly', () => {
      render(
        <DashboardLayout>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
          <div data-testid="child-3">Child 3</div>
        </DashboardLayout>
      )

      expect(screen.getByTestId('child-1')).toBeInTheDocument()
      expect(screen.getByTestId('child-2')).toBeInTheDocument()
      expect(screen.getByTestId('child-3')).toBeInTheDocument()
    })

    it('should handle empty children gracefully', () => {
      render(<DashboardLayout />)

      expect(screen.getByTestId('protected-route')).toBeInTheDocument()
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
      expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    })

    it('should handle null children gracefully', () => {
      render(<DashboardLayout>{null}</DashboardLayout>)

      expect(screen.getByTestId('protected-route')).toBeInTheDocument()
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
      expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should use semantic HTML elements', () => {
      const { container } = render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      const mainElement = container.querySelector('main')
      expect(mainElement).toBeInTheDocument()
      expect(mainElement).toHaveClass('flex-1', 'overflow-y-auto', 'p-6')
    })

    it('should maintain proper document structure', () => {
      render(
        <DashboardLayout>
          <h1>Dashboard Title</h1>
          <p>Dashboard content</p>
        </DashboardLayout>
      )

      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
      expect(screen.getByText('Dashboard content')).toBeInTheDocument()
    })
  })

  describe('Layout Behavior', () => {
    it('should maintain fixed sidebar width', () => {
      const { container } = render(
        <DashboardLayout>
          <div>Content that might be very long and should not affect sidebar width</div>
        </DashboardLayout>
      )

      const sidebarContainer = container.querySelector('.w-64.flex-shrink-0')
      expect(sidebarContainer).toBeInTheDocument()
      expect(sidebarContainer).toHaveClass('flex-shrink-0')
    })

    it('should allow main content to scroll', () => {
      const { container } = render(
        <DashboardLayout>
          <div style={{ height: '2000px' }}>Very tall content</div>
        </DashboardLayout>
      )

      const mainContent = container.querySelector('main')
      expect(mainContent).toHaveClass('overflow-y-auto')
    })

    it('should prevent layout overflow', () => {
      const { container } = render(
        <DashboardLayout>
          <div>Test Content</div>
        </DashboardLayout>
      )

      const mainContainer = container.querySelector('.flex-1.flex.flex-col.overflow-hidden')
      expect(mainContainer).toHaveClass('overflow-hidden')
    })
  })

  describe('Error Handling', () => {
    it('should wrap content in ErrorBoundary for error catching', () => {
      render(
        <DashboardLayout>
          <div>Content that might throw errors</div>
        </DashboardLayout>
      )

      const errorBoundary = screen.getByTestId('error-boundary')
      expect(errorBoundary).toBeInTheDocument()
      expect(errorBoundary).toContainElement(screen.getByText('Content that might throw errors'))
    })
  })

  describe('Authentication Integration', () => {
    it('should wrap entire layout in ProtectedRoute', () => {
      render(
        <DashboardLayout>
          <div>Protected Content</div>
        </DashboardLayout>
      )

      const protectedRoute = screen.getByTestId('protected-route')
      expect(protectedRoute).toBeInTheDocument()
      expect(protectedRoute).toContainElement(screen.getByText('Protected Content'))
    })
  })

  describe('Component Props', () => {
    it('should accept and render React.ReactNode children', () => {
      const complexChild = (
        <div>
          <h2>Complex Child</h2>
          <ul>
            <li>Item 1</li>
            <li>Item 2</li>
          </ul>
          <button>Action Button</button>
        </div>
      )

      render(<DashboardLayout>{complexChild}</DashboardLayout>)

      expect(screen.getByText('Complex Child')).toBeInTheDocument()
      expect(screen.getByText('Item 1')).toBeInTheDocument()
      expect(screen.getByText('Item 2')).toBeInTheDocument()
      expect(screen.getByText('Action Button')).toBeInTheDocument()
    })
  })
})
