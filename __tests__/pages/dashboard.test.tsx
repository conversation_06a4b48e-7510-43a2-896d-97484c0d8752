import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import DashboardPage from '../../app/dashboard/page'
import { useAuth } from '../../lib/hooks'
import { DASHBOARD_ROUTES } from '../../lib/constants'

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock useAuth hook
jest.mock('../../lib/hooks', () => ({
  useAuth: jest.fn(),
}))

// Mock constants
jest.mock('../../lib/constants', () => ({
  DASHBOARD_ROUTES: {
    admin: '/dashboard/admin',
    doctor: '/dashboard/doctor',
    patient: '/dashboard/patient',
  },
}))

// Mock UI components
jest.mock('../../components/ui', () => ({
  LoadingState: ({ message }: { message: string }) => (
    <div data-testid="loading-state">{message}</div>
  ),
}))

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

describe('Dashboard Page', () => {
  const mockPush = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    })
  })

  describe('Loading States', () => {
    it('should show loading state when auth is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: true,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      expect(screen.getByTestId('loading-state')).toBeInTheDocument()
      expect(screen.getByText('Loading dashboard...')).toBeInTheDocument()
    })

    it('should show loading state when user is null and not loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      expect(screen.getByTestId('loading-state')).toBeInTheDocument()
      expect(screen.getByText('Loading dashboard...')).toBeInTheDocument()
    })
  })

  describe('Role-based Redirects', () => {
    it('should redirect admin user to admin dashboard', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard/admin')
      })
    })

    it('should redirect doctor user to doctor dashboard', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '2', email: '<EMAIL>', role: 'doctor' },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard/doctor')
      })
    })

    it('should redirect patient user to patient dashboard', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '3', email: '<EMAIL>', role: 'patient' },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard/patient')
      })
    })
  })

  describe('Effect Dependencies', () => {
    it('should not redirect when loading is true', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        loading: true,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should not redirect when user is null', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should re-run effect when user changes', async () => {
      const { rerender } = render(<DashboardPage />)

      // First render with admin user
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      rerender(<DashboardPage />)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard/admin')
      })

      // Clear mock calls
      mockPush.mockClear()

      // Second render with doctor user
      mockUseAuth.mockReturnValue({
        user: { id: '2', email: '<EMAIL>', role: 'doctor' },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      rerender(<DashboardPage />)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard/doctor')
      })
    })
  })

  describe('Component Rendering', () => {
    it('should return null when user is authenticated and not loading', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      const { container } = render(<DashboardPage />)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard/admin')
      })

      // Component should render null after redirect
      expect(container.firstChild).toBeNull()
    })

    it('should handle unknown user role gracefully', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '4', email: '<EMAIL>', role: 'unknown' as any },
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        logout: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<DashboardPage />)

      await waitFor(() => {
        // Should attempt to redirect with undefined route
        expect(mockPush).toHaveBeenCalledWith(undefined)
      })
    })
  })
})
