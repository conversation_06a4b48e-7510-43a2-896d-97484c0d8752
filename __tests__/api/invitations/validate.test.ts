import { NextRequest } from 'next/server'
import { GET } from '../../../app/api/invitations/validate/route'
import InvitationService from '../../../lib/services/invitation.service'

// Mock Firebase configuration
jest.mock('../../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
}))

// Mock dependencies
jest.mock('../../../lib/database')
jest.mock('../../../lib/services/invitation.service')

const mockInvitationService = {
  validateInvitation: jest.fn(),
}

const MockedInvitationService = InvitationService as jest.MockedClass<typeof InvitationService>

describe('/api/invitations/validate', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    MockedInvitationService.mockImplementation(() => mockInvitationService as any)
  })

  const createMockRequest = (searchParams: Record<string, string>): NextRequest => {
    const url = new URL('http://localhost:3000/api/invitations/validate')
    Object.entries(searchParams).forEach(([key, value]) => {
      url.searchParams.set(key, value)
    })

    return {
      url: url.toString(),
    } as any
  }

  const mockValidInvitation = {
    id: 'invitation-123',
    email: '<EMAIL>',
    token: 'valid-token-123',
    isUsed: false,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  describe('Successful Validation', () => {
    it('should successfully validate a valid invitation', async () => {
      const validationResult = {
        isValid: true,
        invitation: mockValidInvitation,
        message: 'Valid invitation',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'valid-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith('valid-token-123')
    })

    it('should return validation result for unused invitation', async () => {
      const validationResult = {
        isValid: true,
        invitation: { ...mockValidInvitation, isUsed: false },
        message: 'Valid invitation',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'unused-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(responseData.invitation.isUsed).toBe(false)
    })

    it('should return validation result for non-expired invitation', async () => {
      const futureDate = new Date(Date.now() + 48 * 60 * 60 * 1000) // 48 hours from now
      const validationResult = {
        isValid: true,
        invitation: { ...mockValidInvitation, expiresAt: futureDate },
        message: 'Valid invitation',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'future-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(new Date(responseData.invitation.expiresAt).getTime()).toBeGreaterThan(Date.now())
    })
  })

  describe('Invalid Invitations', () => {
    it('should return invalid result for non-existent token', async () => {
      const validationResult = {
        isValid: false,
        invitation: null,
        message: 'Invitation not found',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'non-existent-token',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual(validationResult)
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith('non-existent-token')
    })

    it('should return invalid result for used invitation', async () => {
      const validationResult = {
        isValid: false,
        invitation: { ...mockValidInvitation, isUsed: true },
        message: 'Invitation has already been used',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'used-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(responseData.invitation.isUsed).toBe(true)
    })

    it('should return invalid result for expired invitation', async () => {
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
      const validationResult = {
        isValid: false,
        invitation: { ...mockValidInvitation, expiresAt: pastDate },
        message: 'Invitation has expired',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'expired-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(new Date(responseData.invitation.expiresAt).getTime()).toBeLessThan(Date.now())
    })

    it('should return invalid result for malformed token', async () => {
      const validationResult = {
        isValid: false,
        invitation: null,
        message: 'Invalid invitation token format',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'malformed-token-!@#$%',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual(validationResult)
    })
  })

  describe('Request Validation', () => {
    it('should return 400 when token is missing', async () => {
      const request = createMockRequest({})

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({
        isValid: false,
        message: 'Invitation token is required',
      })
      expect(mockInvitationService.validateInvitation).not.toHaveBeenCalled()
    })

    it('should return 400 when token is empty string', async () => {
      const request = createMockRequest({
        token: '',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({
        isValid: false,
        message: 'Invitation token is required',
      })
      expect(mockInvitationService.validateInvitation).not.toHaveBeenCalled()
    })

    it('should handle multiple query parameters and only use token', async () => {
      const validationResult = {
        isValid: true,
        invitation: mockValidInvitation,
        message: 'Valid invitation',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: 'valid-token-123',
        extraParam: 'should-be-ignored',
        anotherParam: 'also-ignored',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith('valid-token-123')
    })
  })

  describe('Error Handling', () => {
    it('should handle invitation service errors', async () => {
      mockInvitationService.validateInvitation.mockRejectedValue(new Error('Database connection error'))

      const request = createMockRequest({
        token: 'valid-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({
        isValid: false,
        message: 'Failed to validate invitation',
      })
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith('valid-token-123')
    })

    it('should handle network timeout errors', async () => {
      mockInvitationService.validateInvitation.mockRejectedValue(new Error('Network timeout'))

      const request = createMockRequest({
        token: 'valid-token-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({
        isValid: false,
        message: 'Failed to validate invitation',
      })
    })

    it('should handle malformed URL', async () => {
      // Create a request with malformed URL
      const request = {
        url: 'not-a-valid-url',
      } as any

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({
        isValid: false,
        message: 'Failed to validate invitation',
      })
      expect(mockInvitationService.validateInvitation).not.toHaveBeenCalled()
    })
  })

  describe('Edge Cases', () => {
    it('should handle very long token', async () => {
      const longToken = 'a'.repeat(1000) // Very long token
      const validationResult = {
        isValid: false,
        invitation: null,
        message: 'Invalid token length',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: longToken,
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual(validationResult)
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith(longToken)
    })

    it('should handle URL encoded token', async () => {
      const encodedToken = 'token%2Dwith%2Ddashes' // URL encoded version of 'token-with-dashes'
      const validationResult = {
        isValid: true,
        invitation: mockValidInvitation,
        message: 'Valid invitation',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: encodedToken,
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        ...validationResult,
        invitation: {
          ...validationResult.invitation,
          createdAt: validationResult.invitation.createdAt.toISOString(),
          updatedAt: validationResult.invitation.updatedAt.toISOString(),
          expiresAt: validationResult.invitation.expiresAt.toISOString(),
        }
      })
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith(encodedToken)
    })

    it('should handle special characters in token', async () => {
      const specialToken = 'token-with-special-chars-!@#$%^&*()'
      const validationResult = {
        isValid: false,
        invitation: null,
        message: 'Invalid token format',
      }

      mockInvitationService.validateInvitation.mockResolvedValue(validationResult)

      const request = createMockRequest({
        token: specialToken,
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual(validationResult)
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith(specialToken)
    })
  })
})
