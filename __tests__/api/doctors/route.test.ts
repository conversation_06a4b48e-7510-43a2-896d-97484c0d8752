import { NextRequest } from 'next/server'
import { GET, POST } from '../../../app/api/doctors/route'
import { getDatabaseAdapter } from '../../../lib/database'
import { Doctor, PaginatedResponse } from '../../../lib/types'

// Mock the database adapter
jest.mock('../../../lib/database')
const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

// Mock the authentication middleware
jest.mock('../../../lib/middleware/api-auth', () => ({
  withUserAuth: (handler: any) => handler,
  withAdminAuth: (handler: any) => handler,
  isAdmin: (user: any) => user.role === 'admin',
  isDoctor: (user: any) => user.role === 'doctor' || user.role === 'admin',
}))

describe('/api/doctors', () => {
  const mockDataAdapter = {
    getDoctors: jest.fn(),
    createDoctor: jest.fn(),
    getDoctorById: jest.fn(),
    getDoctorByUserId: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
  })

  describe('GET /api/doctors', () => {
    const mockPaginatedResponse: PaginatedResponse<Doctor> = {
      data: [
        {
          id: 'doctor-1',
          firstName: 'Dr. Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+**********',
          specialization: 'Cardiology',
          licenseNumber: 'MD123456',
          department: 'Cardiology',
          experience: 10,
          education: [],
          patients: [],
          schedule: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }

    it('should allow admin to get all doctors', async () => {
      mockDataAdapter.getDoctors.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/doctors')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].firstName).toBe('Dr. Jane')
      expect(mockDataAdapter.getDoctors).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })
    })

    it('should allow doctor to get all doctors', async () => {
      mockDataAdapter.getDoctors.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/doctors')
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
    })

    it('should allow patient to get all doctors', async () => {
      mockDataAdapter.getDoctors.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/doctors')
      ;(request as any).user = { uid: 'patient-1', role: 'patient' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
    })

    it('should handle pagination parameters', async () => {
      mockDataAdapter.getDoctors.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/doctors?page=2&limit=5&sortBy=firstName&sortOrder=asc')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockDataAdapter.getDoctors).toHaveBeenCalledWith({
        page: 2,
        limit: 5,
        sortBy: 'firstName',
        sortOrder: 'asc'
      })
    })

    it('should handle database errors', async () => {
      mockDataAdapter.getDoctors.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/doctors')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to fetch doctors')
    })
  })

  describe('POST /api/doctors', () => {
    const mockDoctor: Doctor = {
      id: 'doctor-1',
      firstName: 'Dr. Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+**********',
      specialization: 'Cardiology',
      licenseNumber: 'MD123456',
      department: 'Cardiology',
      experience: 10,
      education: [],
      patients: [],
      schedule: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    it('should allow admin to create doctor', async () => {
      mockDataAdapter.createDoctor.mockResolvedValue(mockDoctor)

      const doctorData = {
        firstName: 'Dr. Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        specialization: 'Cardiology',
        licenseNumber: 'MD123456',
        department: 'Cardiology',
        experience: 10
      }

      const request = new NextRequest('http://localhost:3000/api/doctors', {
        method: 'POST',
        body: JSON.stringify(doctorData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.firstName).toBe('Dr. Jane')
      expect(mockDataAdapter.createDoctor).toHaveBeenCalled()
    })

    it('should validate required fields', async () => {
      const doctorData = {
        firstName: 'Dr. Jane'
        // Missing required fields
      }

      const request = new NextRequest('http://localhost:3000/api/doctors', {
        method: 'POST',
        body: JSON.stringify(doctorData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Missing required fields')
      expect(mockDataAdapter.createDoctor).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const doctorData = {
        firstName: 'Dr. Jane',
        lastName: 'Smith',
        email: 'invalid-email',
        specialization: 'Cardiology',
        licenseNumber: 'MD123456',
        department: 'Cardiology'
      }

      const request = new NextRequest('http://localhost:3000/api/doctors', {
        method: 'POST',
        body: JSON.stringify(doctorData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid email format')
      expect(mockDataAdapter.createDoctor).not.toHaveBeenCalled()
    })

    it('should validate experience field', async () => {
      const doctorData = {
        firstName: 'Dr. Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        specialization: 'Cardiology',
        licenseNumber: 'MD123456',
        department: 'Cardiology',
        experience: -5 // Invalid negative experience
      }

      const request = new NextRequest('http://localhost:3000/api/doctors', {
        method: 'POST',
        body: JSON.stringify(doctorData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Experience must be a non-negative number')
      expect(mockDataAdapter.createDoctor).not.toHaveBeenCalled()
    })

    it('should handle database errors', async () => {
      mockDataAdapter.createDoctor.mockRejectedValue(new Error('Database error'))

      const doctorData = {
        firstName: 'Dr. Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        specialization: 'Cardiology',
        licenseNumber: 'MD123456',
        department: 'Cardiology'
      }

      const request = new NextRequest('http://localhost:3000/api/doctors', {
        method: 'POST',
        body: JSON.stringify(doctorData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create doctor')
    })
  })
})
