import { NextRequest } from 'next/server'
import { GET, POST } from '../../../app/api/medical-records/route'
import { getDatabaseAdapter } from '../../../lib/database'
import { MedicalRecord, PaginatedResponse } from '../../../lib/types'

// Mock the database adapter
jest.mock('../../../lib/database')
const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

// Mock the authentication middleware
jest.mock('../../../lib/middleware/api-auth', () => ({
  withUserAuth: (handler: any) => handler,
  withDoctorAuth: (handler: any) => handler,
  isAdmin: (user: any) => user.role === 'admin',
  isDoctor: (user: any) => user.role === 'doctor' || user.role === 'admin',
}))

describe('/api/medical-records', () => {
  const mockDataAdapter = {
    getMedicalRecords: jest.fn(),
    getMedicalRecordsByPatient: jest.fn(),
    createMedicalRecord: jest.fn(),
    getPatientById: jest.fn(),
    getPatientByUserId: jest.fn(),
    getDoctorByUserId: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
  })

  describe('GET /api/medical-records', () => {
    const mockMedicalRecord: MedicalRecord = {
      id: 'record-1',
      patientId: 'patient-1',
      doctorId: 'doctor-1',
      date: new Date(),
      diagnosis: 'Common cold',
      treatment: 'Rest and fluids',
      medications: [],
      notes: 'Patient feeling better',
      attachments: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const mockPaginatedResponse: PaginatedResponse<MedicalRecord> = {
      data: [mockMedicalRecord],
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }

    it('should allow admin to get all medical records', async () => {
      mockDataAdapter.getMedicalRecords.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/medical-records')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].diagnosis).toBe('Common cold')
      expect(mockDataAdapter.getMedicalRecords).toHaveBeenCalled()
    })

    it('should allow patient to get their own medical records', async () => {
      mockDataAdapter.getMedicalRecordsByPatient.mockResolvedValue(mockPaginatedResponse)
      mockDataAdapter.getPatientByUserId.mockResolvedValue({ id: 'patient-1', uid: 'user-1' })

      const request = new NextRequest('http://localhost:3000/api/medical-records?patientId=patient-1')
      ;(request as any).user = { uid: 'user-1', role: 'patient' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(mockDataAdapter.getMedicalRecordsByPatient).toHaveBeenCalledWith('patient-1', expect.any(Object))
    })

    it('should deny patient access to other patient records', async () => {
      mockDataAdapter.getPatientByUserId.mockResolvedValue({ id: 'patient-2', uid: 'user-1' })

      const request = new NextRequest('http://localhost:3000/api/medical-records?patientId=patient-1')
      ;(request as any).user = { uid: 'user-1', role: 'patient' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('Access denied')
    })

    it('should deny non-admin access to all records', async () => {
      const request = new NextRequest('http://localhost:3000/api/medical-records')
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('Only administrators can view all medical records')
    })

    it('should handle database errors', async () => {
      mockDataAdapter.getMedicalRecords.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/medical-records')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to fetch medical records')
    })
  })

  describe('POST /api/medical-records', () => {
    const mockMedicalRecord: MedicalRecord = {
      id: 'record-1',
      patientId: 'patient-1',
      doctorId: 'doctor-1',
      date: new Date(),
      diagnosis: 'Common cold',
      treatment: 'Rest and fluids',
      medications: [],
      notes: 'Patient feeling better',
      attachments: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    it('should allow doctor to create medical record', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue({ id: 'patient-1' })
      mockDataAdapter.getDoctorByUserId.mockResolvedValue({ id: 'doctor-1' })
      mockDataAdapter.createMedicalRecord.mockResolvedValue(mockMedicalRecord)

      const recordData = {
        patientId: 'patient-1',
        diagnosis: 'Common cold',
        treatment: 'Rest and fluids',
        notes: 'Patient feeling better'
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'doctor-user-1', role: 'doctor' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.diagnosis).toBe('Common cold')
      expect(mockDataAdapter.createMedicalRecord).toHaveBeenCalled()
    })

    it('should allow admin to create medical record with specified doctor', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue({ id: 'patient-1' })
      mockDataAdapter.createMedicalRecord.mockResolvedValue(mockMedicalRecord)

      const recordData = {
        patientId: 'patient-1',
        doctorId: 'doctor-1',
        diagnosis: 'Common cold',
        treatment: 'Rest and fluids'
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.diagnosis).toBe('Common cold')
    })

    it('should validate required fields', async () => {
      const recordData = {
        patientId: 'patient-1'
        // Missing diagnosis
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Missing required fields')
      expect(mockDataAdapter.createMedicalRecord).not.toHaveBeenCalled()
    })

    it('should validate patient exists', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(null)

      const recordData = {
        patientId: 'patient-1',
        diagnosis: 'Common cold'
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Patient not found')
      expect(mockDataAdapter.createMedicalRecord).not.toHaveBeenCalled()
    })

    it('should require doctor ID when admin creates record', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue({ id: 'patient-1' })

      const recordData = {
        patientId: 'patient-1',
        diagnosis: 'Common cold'
        // Missing doctorId for admin
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Doctor ID is required when admin creates medical records')
    })

    it('should validate doctor profile exists for doctor user', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue({ id: 'patient-1' })
      mockDataAdapter.getDoctorByUserId.mockResolvedValue(null)

      const recordData = {
        patientId: 'patient-1',
        diagnosis: 'Common cold'
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Doctor profile not found')
    })

    it('should handle database errors', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue({ id: 'patient-1' })
      mockDataAdapter.getDoctorByUserId.mockResolvedValue({ id: 'doctor-1' })
      mockDataAdapter.createMedicalRecord.mockRejectedValue(new Error('Database error'))

      const recordData = {
        patientId: 'patient-1',
        diagnosis: 'Common cold'
      }

      const request = new NextRequest('http://localhost:3000/api/medical-records', {
        method: 'POST',
        body: JSON.stringify(recordData)
      })
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create medical record')
    })
  })
})
