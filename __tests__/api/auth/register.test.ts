import { NextRequest } from 'next/server'
import { POST } from '../../../app/api/auth/register/route'
import { getDatabaseAdapter } from '../../../lib/database'
import InvitationService from '../../../lib/services/invitation.service'

// Mock Firebase configuration
jest.mock('../../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
}))

// Mock dependencies
jest.mock('../../../lib/database')
jest.mock('../../../lib/services/invitation.service')

const mockDataAdapter = {
  createUser: jest.fn(),
  getUserByFirebaseUid: jest.fn(),
  updateUserRole: jest.fn(),
}

const mockInvitationService = {
  validateInvitation: jest.fn(),
  markInvitationAsUsed: jest.fn(),
}

const MockedGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>
const MockedInvitationService = InvitationService as jest.MockedClass<typeof InvitationService>

describe('/api/auth/register', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    MockedGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
    MockedInvitationService.mockImplementation(() => mockInvitationService as any)
  })

  const createMockRequest = (body: any): NextRequest => {
    return {
      json: jest.fn().mockResolvedValue(body),
    } as any
  }

  const mockUserData = {
    firebaseUid: 'firebase-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
  }

  describe('Patient Registration (without invitation)', () => {
    it('should successfully register a patient', async () => {
      const mockUser = {
        uid: 'user-123',
        firebaseUid: 'firebase-uid-123',
        email: '<EMAIL>',
        displayName: 'Test User',
        role: 'patient',
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockDataAdapter.createUser.mockResolvedValue(mockUser)

      const request = createMockRequest({
        userData: mockUserData,
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...mockUser,
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        }
      })
      expect(mockDataAdapter.createUser).toHaveBeenCalledWith({
        firebaseUid: mockUserData.firebaseUid,
        email: mockUserData.email,
        displayName: mockUserData.displayName,
        role: 'patient',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      })
    })

    it('should handle database errors during patient registration', async () => {
      mockDataAdapter.createUser.mockRejectedValue(new Error('Database error'))

      const request = createMockRequest({
        userData: mockUserData,
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to register user' })
    })
  })

  describe('Doctor Registration (with invitation)', () => {
    const invitationToken = 'valid-invitation-token'

    it('should successfully register a doctor with valid invitation', async () => {
      const mockInvitation = {
        id: 'invitation-123',
        email: '<EMAIL>',
        token: invitationToken,
        isUsed: false,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const mockUser = {
        uid: 'user-123',
        firebaseUid: 'firebase-uid-123',
        email: '<EMAIL>',
        displayName: 'Dr. Test',
        role: 'doctor',
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockInvitationService.validateInvitation.mockResolvedValue({
        isValid: true,
        invitation: mockInvitation,
        message: 'Valid invitation',
      })
      mockDataAdapter.createUser.mockResolvedValue(mockUser)
      mockInvitationService.markInvitationAsUsed.mockResolvedValue({ success: true })

      const request = createMockRequest({
        userData: {
          ...mockUserData,
          email: '<EMAIL>',
          displayName: 'Dr. Test',
        },
        invitationToken,
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...mockUser,
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        }
      })
      expect(mockInvitationService.validateInvitation).toHaveBeenCalledWith(invitationToken)
      expect(mockDataAdapter.createUser).toHaveBeenCalledWith({
        firebaseUid: mockUserData.firebaseUid,
        email: '<EMAIL>',
        displayName: 'Dr. Test',
        role: 'doctor',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      })
      expect(mockInvitationService.markInvitationAsUsed).toHaveBeenCalledWith(invitationToken)
    })

    it('should reject registration with invalid invitation', async () => {
      mockInvitationService.validateInvitation.mockResolvedValue({
        isValid: false,
        invitation: null,
        message: 'Invalid invitation token',
      })

      const request = createMockRequest({
        userData: mockUserData,
        invitationToken: 'invalid-token',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Invalid invitation token' })
      expect(mockDataAdapter.createUser).not.toHaveBeenCalled()
      expect(mockInvitationService.markInvitationAsUsed).not.toHaveBeenCalled()
    })

    it('should reject registration when email does not match invitation', async () => {
      const mockInvitation = {
        id: 'invitation-123',
        email: '<EMAIL>',
        token: invitationToken,
        isUsed: false,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockInvitationService.validateInvitation.mockResolvedValue({
        isValid: true,
        invitation: mockInvitation,
        message: 'Valid invitation',
      })

      const request = createMockRequest({
        userData: {
          ...mockUserData,
          email: '<EMAIL>', // Different email
        },
        invitationToken,
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Email must match the invitation' })
      expect(mockDataAdapter.createUser).not.toHaveBeenCalled()
      expect(mockInvitationService.markInvitationAsUsed).not.toHaveBeenCalled()
    })

    it('should handle database errors during doctor registration', async () => {
      const mockInvitation = {
        id: 'invitation-123',
        email: '<EMAIL>',
        token: invitationToken,
        isUsed: false,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockInvitationService.validateInvitation.mockResolvedValue({
        isValid: true,
        invitation: mockInvitation,
        message: 'Valid invitation',
      })
      mockDataAdapter.createUser.mockRejectedValue(new Error('Database error'))

      const request = createMockRequest({
        userData: {
          ...mockUserData,
          email: '<EMAIL>',
        },
        invitationToken,
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to register user' })
      expect(mockInvitationService.markInvitationAsUsed).not.toHaveBeenCalled()
    })

    it('should handle invitation service errors', async () => {
      mockInvitationService.validateInvitation.mockRejectedValue(new Error('Service error'))

      const request = createMockRequest({
        userData: mockUserData,
        invitationToken,
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to register user' })
      expect(mockDataAdapter.createUser).not.toHaveBeenCalled()
    })
  })

  describe('Request Validation', () => {
    it('should handle malformed JSON', async () => {
      const request = {
        json: jest.fn().mockRejectedValue(new Error('Invalid JSON')),
      } as any

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to register user' })
    })
  })
})
