import { NextRequest } from 'next/server'
import { POST } from '../../../app/api/auth/update-role/route'
import { getDatabaseAdapter } from '../../../lib/database'

// Mock Firebase configuration
jest.mock('../../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
}))

// Mock dependencies
jest.mock('../../../lib/database')

const mockDataAdapter = {
  getUserByFirebaseUid: jest.fn(),
  updateUserRole: jest.fn(),
}

const MockedGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

describe('/api/auth/update-role', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    MockedGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
  })

  const createMockRequest = (body: any): NextRequest => {
    return {
      json: jest.fn().mockResolvedValue(body),
    } as any
  }

  const mockUser = {
    uid: 'user-123',
    firebaseUid: 'firebase-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    role: 'patient',
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  describe('Successful Role Updates', () => {
    it('should successfully update user role from patient to doctor', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)
      mockDataAdapter.updateUserRole.mockResolvedValue(undefined)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: 'doctor',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({ success: true })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123')
      expect(mockDataAdapter.updateUserRole).toHaveBeenCalledWith('user-123', 'doctor')
    })

    it('should successfully update user role from doctor to admin', async () => {
      const doctorUser = { ...mockUser, role: 'doctor' }
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(doctorUser)
      mockDataAdapter.updateUserRole.mockResolvedValue(undefined)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: 'admin',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({ success: true })
      expect(mockDataAdapter.updateUserRole).toHaveBeenCalledWith('user-123', 'admin')
    })

    it('should successfully update user role to patient', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)
      mockDataAdapter.updateUserRole.mockResolvedValue(undefined)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: 'patient',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({ success: true })
      expect(mockDataAdapter.updateUserRole).toHaveBeenCalledWith('user-123', 'patient')
    })
  })

  describe('Request Validation', () => {
    it('should return 400 when firebaseUid is missing', async () => {
      const request = createMockRequest({
        role: 'doctor',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID and role are required' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
      expect(mockDataAdapter.updateUserRole).not.toHaveBeenCalled()
    })

    it('should return 400 when role is missing', async () => {
      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID and role are required' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
      expect(mockDataAdapter.updateUserRole).not.toHaveBeenCalled()
    })

    it('should return 400 when both firebaseUid and role are missing', async () => {
      const request = createMockRequest({})

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID and role are required' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
      expect(mockDataAdapter.updateUserRole).not.toHaveBeenCalled()
    })

    it('should return 400 when firebaseUid is empty string', async () => {
      const request = createMockRequest({
        firebaseUid: '',
        role: 'doctor',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID and role are required' })
    })

    it('should return 400 when role is empty string', async () => {
      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: '',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID and role are required' })
    })
  })

  describe('User Not Found', () => {
    it('should return 404 when user is not found', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)

      const request = createMockRequest({
        firebaseUid: 'non-existent-uid',
        role: 'doctor',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(404)
      expect(responseData).toEqual({ error: 'User not found' })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('non-existent-uid')
      expect(mockDataAdapter.updateUserRole).not.toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors when getting user', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockRejectedValue(new Error('Database connection error'))

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: 'doctor',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to update user role' })
      expect(mockDataAdapter.updateUserRole).not.toHaveBeenCalled()
    })

    it('should handle database errors when updating role', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)
      mockDataAdapter.updateUserRole.mockRejectedValue(new Error('Update failed'))

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: 'doctor',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to update user role' })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123')
      expect(mockDataAdapter.updateUserRole).toHaveBeenCalledWith('user-123', 'doctor')
    })

    it('should handle malformed JSON', async () => {
      const request = {
        json: jest.fn().mockRejectedValue(new Error('Invalid JSON')),
      } as any

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to update user role' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
      expect(mockDataAdapter.updateUserRole).not.toHaveBeenCalled()
    })
  })

  describe('Role Validation', () => {
    it('should accept valid role values', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)
      mockDataAdapter.updateUserRole.mockResolvedValue(undefined)

      const validRoles = ['patient', 'doctor', 'admin']

      for (const role of validRoles) {
        const request = createMockRequest({
          firebaseUid: 'firebase-uid-123',
          role,
        })

        const response = await POST(request)
        const responseData = await response.json()

        expect(response.status).toBe(200)
        expect(responseData).toEqual({ success: true })
        expect(mockDataAdapter.updateUserRole).toHaveBeenCalledWith('user-123', role)
      }
    })

    // Note: The API doesn't validate role values at the API level,
    // it relies on TypeScript types and database constraints
    it('should pass through invalid role values to database adapter', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)
      mockDataAdapter.updateUserRole.mockResolvedValue(undefined)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        role: 'invalid-role',
      })

      const response = await POST(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({ success: true })
      expect(mockDataAdapter.updateUserRole).toHaveBeenCalledWith('user-123', 'invalid-role')
    })
  })
})
