import { NextRequest } from 'next/server'
import { GET } from '../../../app/api/auth/user/route'
import { getDatabaseAdapter } from '../../../lib/database'

// Mock Firebase configuration
jest.mock('../../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
}))

// Mock dependencies
jest.mock('../../../lib/database')

const mockDataAdapter = {
  getUserByFirebaseUid: jest.fn(),
}

const MockedGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

describe('/api/auth/user', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    MockedGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
  })

  const createMockRequest = (searchParams: Record<string, string>): NextRequest => {
    const url = new URL('http://localhost:3000/api/auth/user')
    Object.entries(searchParams).forEach(([key, value]) => {
      url.searchParams.set(key, value)
    })

    return {
      url: url.toString(),
    } as any
  }

  const mockUser = {
    uid: 'user-123',
    firebaseUid: 'firebase-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    role: 'patient',
    createdAt: new Date('2025-01-01T00:00:00.000Z'),
    updatedAt: new Date('2025-01-01T00:00:00.000Z'),
  }

  describe('Successful User Retrieval', () => {
    it('should successfully get user by Firebase UID', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...mockUser,
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        }
      })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123')
    })

    it('should successfully get patient user', async () => {
      const patientUser = { ...mockUser, role: 'patient' }
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(patientUser)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...patientUser,
          createdAt: patientUser.createdAt.toISOString(),
          updatedAt: patientUser.updatedAt.toISOString(),
        }
      })
    })

    it('should successfully get doctor user', async () => {
      const doctorUser = { ...mockUser, role: 'doctor' }
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(doctorUser)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...doctorUser,
          createdAt: doctorUser.createdAt.toISOString(),
          updatedAt: doctorUser.updatedAt.toISOString(),
        }
      })
    })

    it('should successfully get admin user', async () => {
      const adminUser = { ...mockUser, role: 'admin' }
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(adminUser)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...adminUser,
          createdAt: adminUser.createdAt.toISOString(),
          updatedAt: adminUser.updatedAt.toISOString(),
        }
      })
    })
  })

  describe('Request Validation', () => {
    it('should return 400 when firebaseUid is missing', async () => {
      const request = createMockRequest({})

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID is required' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
    })

    it('should return 400 when firebaseUid is empty string', async () => {
      const request = createMockRequest({
        firebaseUid: '',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({ error: 'Firebase UID is required' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
    })

    it('should handle multiple query parameters and only use firebaseUid', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
        extraParam: 'should-be-ignored',
        anotherParam: 'also-ignored',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...mockUser,
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        }
      })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123')
    })
  })

  describe('User Not Found', () => {
    it('should return 404 when user is not found', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)

      const request = createMockRequest({
        firebaseUid: 'non-existent-uid',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(404)
      expect(responseData).toEqual({ error: 'User not found' })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('non-existent-uid')
    })

    it('should return 404 when user is undefined', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(undefined)

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(404)
      expect(responseData).toEqual({ error: 'User not found' })
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockRejectedValue(new Error('Database connection error'))

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to get user' })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123')
    })

    it('should handle network timeout errors', async () => {
      mockDataAdapter.getUserByFirebaseUid.mockRejectedValue(new Error('Network timeout'))

      const request = createMockRequest({
        firebaseUid: 'firebase-uid-123',
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to get user' })
    })

    it('should handle malformed URL', async () => {
      // Create a request with malformed URL
      const request = {
        url: 'not-a-valid-url',
      } as any

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({ error: 'Failed to get user' })
      expect(mockDataAdapter.getUserByFirebaseUid).not.toHaveBeenCalled()
    })
  })

  describe('Edge Cases', () => {
    it('should handle special characters in firebaseUid', async () => {
      const specialUid = 'firebase-uid-with-special-chars-!@#$%^&*()'
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue({
        ...mockUser,
        firebaseUid: specialUid,
      })

      const request = createMockRequest({
        firebaseUid: specialUid,
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData.user.firebaseUid).toBe(specialUid)
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith(specialUid)
    })

    it('should handle very long firebaseUid', async () => {
      const longUid = 'a'.repeat(1000) // Very long UID
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue({
        ...mockUser,
        firebaseUid: longUid,
      })

      const request = createMockRequest({
        firebaseUid: longUid,
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData.user.firebaseUid).toBe(longUid)
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith(longUid)
    })

    it('should handle URL encoded firebaseUid', async () => {
      const encodedUid = 'firebase%2Duid%2D123' // URL encoded version of 'firebase-uid-123'
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockUser)

      const request = createMockRequest({
        firebaseUid: encodedUid,
      })

      const response = await GET(request)
      const responseData = await response.json()

      expect(response.status).toBe(200)
      expect(responseData).toEqual({
        user: {
          ...mockUser,
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        }
      })
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith(encodedUid)
    })
  })
})
