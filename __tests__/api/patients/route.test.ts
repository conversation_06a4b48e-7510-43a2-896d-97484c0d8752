import { NextRequest } from 'next/server'
import { GET, POST } from '../../../app/api/patients/route'
import { getDatabaseAdapter } from '../../../lib/database'
import { Patient, PaginatedResponse } from '../../../lib/types'

// Mock the database adapter
jest.mock('../../../lib/database')
const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

// Mock the authentication middleware
jest.mock('../../../lib/middleware/api-auth', () => ({
  withUserAuth: (handler: any) => handler,
  withAdminAuth: (handler: any) => handler,
  isAdmin: (user: any) => user.role === 'admin',
  isDoctor: (user: any) => user.role === 'doctor' || user.role === 'admin',
}))

describe('/api/patients', () => {
  const mockDataAdapter = {
    getPatients: jest.fn(),
    createPatient: jest.fn(),
    getPatientById: jest.fn(),
    getPatientByUserId: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
  })

  describe('GET /api/patients', () => {
    const mockPaginatedResponse: PaginatedResponse<Patient> = {
      data: [
        {
          id: 'patient-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+**********',
          dateOfBirth: new Date('1990-01-01'),
          gender: 'male',
          address: {
            street: '123 Main St',
            city: 'Anytown',
            state: 'ST',
            zipCode: '12345',
            country: 'USA'
          },
          emergencyContact: {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '+**********'
          },
          medicalHistory: [],
          appointments: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }

    it('should allow admin to get all patients', async () => {
      mockDataAdapter.getPatients.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/patients')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].firstName).toBe('John')
      expect(mockDataAdapter.getPatients).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })
    })

    it('should allow doctor to get all patients', async () => {
      mockDataAdapter.getPatients.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/patients')
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
    })

    it('should deny access to patients', async () => {
      const request = new NextRequest('http://localhost:3000/api/patients')
      ;(request as any).user = { uid: 'patient-1', role: 'patient' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('Access denied')
      expect(mockDataAdapter.getPatients).not.toHaveBeenCalled()
    })

    it('should handle pagination parameters', async () => {
      mockDataAdapter.getPatients.mockResolvedValue(mockPaginatedResponse)

      const request = new NextRequest('http://localhost:3000/api/patients?page=2&limit=5&sortBy=firstName&sortOrder=asc')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockDataAdapter.getPatients).toHaveBeenCalledWith({
        page: 2,
        limit: 5,
        sortBy: 'firstName',
        sortOrder: 'asc'
      })
    })

    it('should handle database errors', async () => {
      mockDataAdapter.getPatients.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/patients')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to fetch patients')
    })
  })

  describe('POST /api/patients', () => {
    const mockPatient: Patient = {
      id: 'patient-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+**********',
      dateOfBirth: new Date('1990-01-01'),
      gender: 'male',
      address: {
        street: '123 Main St',
        city: 'Anytown',
        state: 'ST',
        zipCode: '12345',
        country: 'USA'
      },
      emergencyContact: {
        name: 'Jane Doe',
        relationship: 'Spouse',
        phone: '+**********'
      },
      medicalHistory: [],
      appointments: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    it('should allow admin to create patient', async () => {
      mockDataAdapter.createPatient.mockResolvedValue(mockPatient)

      const patientData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: '1990-01-01',
        gender: 'male'
      }

      const request = new NextRequest('http://localhost:3000/api/patients', {
        method: 'POST',
        body: JSON.stringify(patientData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.firstName).toBe('John')
      expect(mockDataAdapter.createPatient).toHaveBeenCalled()
    })

    it('should validate required fields', async () => {
      const patientData = {
        firstName: 'John'
        // Missing required fields
      }

      const request = new NextRequest('http://localhost:3000/api/patients', {
        method: 'POST',
        body: JSON.stringify(patientData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Missing required fields')
      expect(mockDataAdapter.createPatient).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const patientData = {
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        dateOfBirth: '1990-01-01'
      }

      const request = new NextRequest('http://localhost:3000/api/patients', {
        method: 'POST',
        body: JSON.stringify(patientData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid email format')
      expect(mockDataAdapter.createPatient).not.toHaveBeenCalled()
    })

    it('should validate date of birth', async () => {
      const patientData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: 'invalid-date'
      }

      const request = new NextRequest('http://localhost:3000/api/patients', {
        method: 'POST',
        body: JSON.stringify(patientData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid date of birth')
      expect(mockDataAdapter.createPatient).not.toHaveBeenCalled()
    })

    it('should handle database errors', async () => {
      mockDataAdapter.createPatient.mockRejectedValue(new Error('Database error'))

      const patientData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: '1990-01-01'
      }

      const request = new NextRequest('http://localhost:3000/api/patients', {
        method: 'POST',
        body: JSON.stringify(patientData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create patient')
    })
  })
})
