import { NextRequest } from 'next/server'
import { GET, PUT, DELETE } from '../../../../app/api/patients/[id]/route'
import { getDatabaseAdapter } from '../../../../lib/database'
import { Patient } from '../../../../lib/types'

// Mock the database adapter
jest.mock('../../../../lib/database')
const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

// Mock the authentication middleware
jest.mock('../../../../lib/middleware/api-auth', () => ({
  withUserAuth: (handler: any) => handler,
  withAdminAuth: (handler: any) => handler,
  isAdmin: (user: any) => user.role === 'admin',
  isDoctor: (user: any) => user.role === 'doctor' || user.role === 'admin',
  isValidUUID: (uuid: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid),
  createErrorResponse: (message: string, status: number, code?: string, details?: any) => 
    new Response(JSON.stringify({ error: message, code, details }), { status })
}))

describe('/api/patients/[id]', () => {
  const mockDataAdapter = {
    getPatientById: jest.fn(),
    getPatientByUserId: jest.fn(),
    updatePatient: jest.fn(),
    deletePatient: jest.fn(),
  }

  const mockPatient: Patient = {
    id: 'patient-1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    dateOfBirth: new Date('1990-01-01'),
    gender: 'male',
    address: {
      street: '123 Main St',
      city: 'Anytown',
      state: 'ST',
      zipCode: '12345',
      country: 'USA'
    },
    emergencyContact: {
      name: 'Jane Doe',
      relationship: 'Spouse',
      phone: '+**********'
    },
    medicalHistory: [],
    appointments: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
  })

  describe('GET /api/patients/[id]', () => {
    it('should allow admin to get any patient', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'patient-1' } }
      const response = await GET(request, context)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.firstName).toBe('John')
      expect(mockDataAdapter.getPatientById).toHaveBeenCalledWith('patient-1')
    })

    it('should allow doctor to get patient', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1')
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const context = { params: { id: 'patient-1' } }
      const response = await GET(request, context)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.firstName).toBe('John')
    })

    it('should allow patient to get their own data', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)
      mockDataAdapter.getPatientByUserId.mockResolvedValue(mockPatient)

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1')
      ;(request as any).user = { uid: 'user-1', role: 'patient' }

      const context = { params: { id: 'patient-1' } }
      const response = await GET(request, context)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.firstName).toBe('John')
    })

    it('should deny patient access to other patient data', async () => {
      mockDataAdapter.getPatientByUserId.mockResolvedValue({ ...mockPatient, id: 'patient-2' })

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1')
      ;(request as any).user = { uid: 'user-1', role: 'patient' }

      const context = { params: { id: 'patient-1' } }
      const response = await GET(request, context)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('Access denied')
    })

    it('should return 404 for non-existent patient', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'patient-1' } }
      const response = await GET(request, context)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Patient not found')
    })

    it('should validate UUID format', async () => {
      const request = new NextRequest('http://localhost:3000/api/patients/invalid-id')
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'invalid-id' } }
      const response = await GET(request, context)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid patient ID format')
    })
  })

  describe('PUT /api/patients/[id]', () => {
    it('should allow admin to update patient', async () => {
      const updatedPatient = { ...mockPatient, firstName: 'Jane' }
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)
      mockDataAdapter.updatePatient.mockResolvedValue(updatedPatient)

      const updateData = { firstName: 'Jane' }
      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'patient-1' } }
      const response = await PUT(request, context)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.firstName).toBe('Jane')
      expect(mockDataAdapter.updatePatient).toHaveBeenCalledWith('patient-1', { firstName: 'Jane' })
    })

    it('should allow doctor to update patient', async () => {
      const updatedPatient = { ...mockPatient, firstName: 'Jane' }
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)
      mockDataAdapter.updatePatient.mockResolvedValue(updatedPatient)

      const updateData = { firstName: 'Jane' }
      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      })
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const context = { params: { id: 'patient-1' } }
      const response = await PUT(request, context)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })

    it('should deny patient access to update', async () => {
      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'PUT',
        body: JSON.stringify({ firstName: 'Jane' })
      })
      ;(request as any).user = { uid: 'user-1', role: 'patient' }

      const context = { params: { id: 'patient-1' } }
      const response = await PUT(request, context)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('Access denied')
    })

    it('should validate email format in updates', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)

      const updateData = { email: 'invalid-email' }
      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'patient-1' } }
      const response = await PUT(request, context)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid email format')
    })

    it('should restrict doctor assignment changes to admins only', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)

      const updateData = { assignedDoctorId: 'doctor-2' }
      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      })
      ;(request as any).user = { uid: 'doctor-1', role: 'doctor' }

      const context = { params: { id: 'patient-1' } }
      const response = await PUT(request, context)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('Only administrators can change doctor assignments')
    })
  })

  describe('DELETE /api/patients/[id]', () => {
    it('should allow admin to delete patient', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(mockPatient)
      mockDataAdapter.deletePatient.mockResolvedValue(undefined)

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'DELETE'
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'patient-1' } }
      const response = await DELETE(request, context)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Patient deleted successfully')
      expect(mockDataAdapter.deletePatient).toHaveBeenCalledWith('patient-1')
    })

    it('should return 404 for non-existent patient', async () => {
      mockDataAdapter.getPatientById.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/patients/patient-1', {
        method: 'DELETE'
      })
      ;(request as any).user = { uid: 'admin-1', role: 'admin' }

      const context = { params: { id: 'patient-1' } }
      const response = await DELETE(request, context)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Patient not found')
    })
  })
})
