import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import ErrorBoundary from '../../components/error-boundary'

// Mock UI components
jest.mock('../../components/ui', () => ({
  Alert: ({ children, variant, ...props }: any) => (
    <div data-testid="alert" data-variant={variant} {...props}>
      {children}
    </div>
  ),
  AlertTitle: ({ children, ...props }: any) => (
    <div data-testid="alert-title" {...props}>
      {children}
    </div>
  ),
  AlertDescription: ({ children, className, ...props }: any) => (
    <div data-testid="alert-description" className={className} {...props}>
      {children}
    </div>
  ),
  Button: ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}))

// Component that throws an error for testing
const ThrowError = ({ shouldThrow = false, errorMessage = 'Test error' }) => {
  if (shouldThrow) {
    throw new Error(errorMessage)
  }
  return <div data-testid="success-component">No error occurred</div>
}

describe('ErrorBoundary Component', () => {
  let consoleSpy: jest.SpyInstance

  beforeEach(() => {
    // Mock console.error to avoid noise in test output
    consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    consoleSpy.mockRestore()
  })

  describe('Normal Operation', () => {
    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('success-component')).toBeInTheDocument()
      expect(screen.getByText('No error occurred')).toBeInTheDocument()
    })

    it('should render multiple children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('child-1')).toBeInTheDocument()
      expect(screen.getByTestId('child-2')).toBeInTheDocument()
      expect(screen.getByTestId('success-component')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should catch and display error with default UI when error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Something went wrong!" />
        </ErrorBoundary>
      )

      // Should not render the child component
      expect(screen.queryByTestId('success-component')).not.toBeInTheDocument()

      // Should render error UI
      expect(screen.getByTestId('alert')).toBeInTheDocument()
      expect(screen.getByTestId('alert')).toHaveAttribute('data-variant', 'destructive')
      expect(screen.getByTestId('alert-title')).toHaveTextContent('Something went wrong')
      expect(screen.getByTestId('alert-description')).toHaveTextContent('Something went wrong!')
      expect(screen.getByText('Try again')).toBeInTheDocument()
    })

    it('should display generic error message when error has no message', () => {
      // Create an error without a message
      const ErrorWithoutMessage = () => {
        const error = new Error()
        error.message = ''
        throw error
      }

      render(
        <ErrorBoundary>
          <ErrorWithoutMessage />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('alert-description')).toHaveTextContent('An unexpected error occurred')
    })

    it('should log error to console when error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Console test error" />
        </ErrorBoundary>
      )

      expect(consoleSpy).toHaveBeenCalledWith(
        'Uncaught error:',
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      )
    })
  })

  describe('Custom Fallback UI', () => {
    it('should render custom fallback when provided', () => {
      const customFallback = <div data-testid="custom-fallback">Custom error UI</div>

      render(
        <ErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument()
      expect(screen.getByText('Custom error UI')).toBeInTheDocument()
      
      // Should not render default error UI
      expect(screen.queryByTestId('alert')).not.toBeInTheDocument()
      expect(screen.queryByText('Try again')).not.toBeInTheDocument()
    })

    it('should render custom fallback with complex JSX', () => {
      const customFallback = (
        <div data-testid="complex-fallback">
          <h1>Oops!</h1>
          <p>Something went wrong with our custom handler</p>
          <button>Custom Action</button>
        </div>
      )

      render(
        <ErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('complex-fallback')).toBeInTheDocument()
      expect(screen.getByText('Oops!')).toBeInTheDocument()
      expect(screen.getByText('Something went wrong with our custom handler')).toBeInTheDocument()
      expect(screen.getByText('Custom Action')).toBeInTheDocument()
    })
  })

  describe('Error Recovery', () => {
    it('should reset error state when Try again button is clicked', () => {
      const TestComponent = () => {
        const [shouldThrow, setShouldThrow] = React.useState(true)
        
        return (
          <div>
            <button 
              data-testid="toggle-error" 
              onClick={() => setShouldThrow(!shouldThrow)}
            >
              Toggle Error
            </button>
            <ErrorBoundary>
              <ThrowError shouldThrow={shouldThrow} />
            </ErrorBoundary>
          </div>
        )
      }

      render(<TestComponent />)

      // Initially should show error
      expect(screen.getByTestId('alert')).toBeInTheDocument()
      expect(screen.getByText('Try again')).toBeInTheDocument()

      // Click try again button
      fireEvent.click(screen.getByText('Try again'))

      // Error should be cleared but component still throws, so error shows again
      expect(screen.getByTestId('alert')).toBeInTheDocument()

      // Now fix the underlying issue
      fireEvent.click(screen.getByTestId('toggle-error'))

      // Click try again after fixing the issue
      fireEvent.click(screen.getByText('Try again'))

      // Should now show success component
      expect(screen.getByTestId('success-component')).toBeInTheDocument()
      expect(screen.queryByTestId('alert')).not.toBeInTheDocument()
    })

    it('should maintain error state until reset is called', () => {
      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Should show error
      expect(screen.getByTestId('alert')).toBeInTheDocument()

      // Re-render with non-throwing component
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )

      // Should still show error until reset
      expect(screen.getByTestId('alert')).toBeInTheDocument()
      expect(screen.queryByTestId('success-component')).not.toBeInTheDocument()
    })
  })

  describe('Component Lifecycle', () => {
    it('should call getDerivedStateFromError when error occurs', () => {
      const spy = jest.spyOn(ErrorBoundary, 'getDerivedStateFromError')

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Lifecycle test" />
        </ErrorBoundary>
      )

      expect(spy).toHaveBeenCalledWith(expect.any(Error))
      expect(spy).toHaveReturnedWith({
        hasError: true,
        error: expect.any(Error)
      })

      spy.mockRestore()
    })

    it('should handle multiple consecutive errors', () => {
      const TestMultipleErrors = ({ errorCount }: { errorCount: number }) => {
        if (errorCount > 0) {
          throw new Error(`Error ${errorCount}`)
        }
        return <div data-testid="no-error">No error</div>
      }

      const { rerender } = render(
        <ErrorBoundary>
          <TestMultipleErrors errorCount={1} />
        </ErrorBoundary>
      )

      // First error
      expect(screen.getByTestId('alert-description')).toHaveTextContent('Error 1')

      // Reset the error boundary first
      fireEvent.click(screen.getByText('Try again'))

      // Re-render with a new ErrorBoundary instance to test second error
      rerender(
        <ErrorBoundary key="second-error">
          <TestMultipleErrors errorCount={2} />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('alert-description')).toHaveTextContent('Error 2')
    })
  })

  describe('Edge Cases', () => {
    it('should handle null error gracefully', () => {
      // Simulate an error boundary that receives null error
      const spy = jest.spyOn(ErrorBoundary, 'getDerivedStateFromError')
      spy.mockReturnValue({ hasError: true, error: undefined })

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByTestId('alert-description')).toHaveTextContent('An unexpected error occurred')

      spy.mockRestore()
    })

    it('should handle error boundary with no children', () => {
      render(<ErrorBoundary />)
      
      // Should render without crashing
      expect(screen.queryByTestId('alert')).not.toBeInTheDocument()
    })

    it('should handle error boundary with null children', () => {
      render(<ErrorBoundary>{null}</ErrorBoundary>)
      
      // Should render without crashing
      expect(screen.queryByTestId('alert')).not.toBeInTheDocument()
    })
  })
})
