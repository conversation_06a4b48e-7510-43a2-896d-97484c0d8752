import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { usePathname } from 'next/navigation'
import { Sidebar } from '../../components/navigation/sidebar'
import { useAuth } from '../../lib/hooks'

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

// Mock useAuth hook
jest.mock('../../lib/hooks', () => ({
  useAuth: jest.fn(),
}))

// Mock UI components
jest.mock('../../components/ui', () => ({
  Button: ({ children, onClick, className, ...props }: any) => (
    <button onClick={onClick} className={className} {...props}>
      {children}
    </button>
  ),
}))

// Mock utils
jest.mock('../../lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}))

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Users: ({ className }: any) => <div data-testid="users-icon" className={className} />,
  UserPlus: ({ className }: any) => <div data-testid="user-plus-icon" className={className} />,
  Calendar: ({ className }: any) => <div data-testid="calendar-icon" className={className} />,
  FileText: ({ className }: any) => <div data-testid="file-text-icon" className={className} />,
  Settings: ({ className }: any) => <div data-testid="settings-icon" className={className} />,
  LogOut: ({ className }: any) => <div data-testid="logout-icon" className={className} />,
  Home: ({ className }: any) => <div data-testid="home-icon" className={className} />,
  Stethoscope: ({ className }: any) => <div data-testid="stethoscope-icon" className={className} />,
  User: ({ className }: any) => <div data-testid="user-icon" className={className} />,
}))

// Mock Next.js Link
jest.mock('next/link', () => {
  return ({ children, href, className, ...props }: any) => (
    <a href={href} className={className} {...props}>
      {children}
    </a>
  )
})

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>

describe('Sidebar Component', () => {
  const mockLogout = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUsePathname.mockReturnValue('/dashboard')
    mockLogout.mockClear()
  })

  describe('Role-based Navigation Rendering', () => {
    it('should render admin navigation items for admin user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<Sidebar />)

      // Admin should see admin-specific navigation items
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Patients')).toBeInTheDocument()
      expect(screen.getByText('Doctors')).toBeInTheDocument()
      expect(screen.getByText('Invite Doctor')).toBeInTheDocument()
      expect(screen.getByText('Appointments')).toBeInTheDocument()
      expect(screen.queryByText('Medical Records')).not.toBeInTheDocument() // Admin doesn't have access
      expect(screen.getByText('Profile')).toBeInTheDocument()
      expect(screen.getByText('Settings')).toBeInTheDocument()
    })

    it('should render doctor navigation items for doctor user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '2', email: '<EMAIL>', role: 'doctor' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<Sidebar />)

      // Doctor should see doctor-specific navigation items
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Patients')).toBeInTheDocument()
      expect(screen.queryByText('Doctors')).not.toBeInTheDocument() // Admin only
      expect(screen.queryByText('Invite Doctor')).not.toBeInTheDocument() // Admin only
      expect(screen.getByText('Appointments')).toBeInTheDocument()
      expect(screen.getByText('Medical Records')).toBeInTheDocument() // Doctor has access
      expect(screen.getByText('Profile')).toBeInTheDocument()
      expect(screen.getByText('Settings')).toBeInTheDocument()
    })

    it('should render patient navigation items for patient user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '3', email: '<EMAIL>', role: 'patient' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<Sidebar />)

      // Patient should see patient-specific navigation items
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.queryByText('Patients')).not.toBeInTheDocument() // Admin/Doctor only
      expect(screen.queryByText('Doctors')).not.toBeInTheDocument() // Admin only
      expect(screen.queryByText('Invite Doctor')).not.toBeInTheDocument() // Admin only
      expect(screen.getByText('Appointments')).toBeInTheDocument()
      expect(screen.getByText('Medical Records')).toBeInTheDocument() // Patient has access
      expect(screen.getByText('Profile')).toBeInTheDocument()
      expect(screen.getByText('Settings')).toBeInTheDocument()
    })

    it('should render no navigation items when user is null', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<Sidebar />)

      // Should still show header but no navigation items
      expect(screen.getByText('PMS')).toBeInTheDocument()
      expect(screen.getByText('Patient Management')).toBeInTheDocument()
      expect(screen.queryByText('Dashboard')).not.toBeInTheDocument()
      expect(screen.queryByText('Patients')).not.toBeInTheDocument()
    })
  })

  describe('Active Navigation State', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })
    })

    it('should highlight active navigation item for exact path match', () => {
      mockUsePathname.mockReturnValue('/dashboard')
      render(<Sidebar />)

      const dashboardLink = screen.getByText('Dashboard').closest('a')
      expect(dashboardLink).toHaveClass('bg-primary text-primary-foreground')
    })

    it('should highlight active navigation item for path prefix match', () => {
      mockUsePathname.mockReturnValue('/dashboard/patients/123')
      render(<Sidebar />)

      const patientsLink = screen.getByText('Patients').closest('a')
      expect(patientsLink).toHaveClass('bg-primary text-primary-foreground')
    })

    it('should not highlight dashboard for sub-paths', () => {
      mockUsePathname.mockReturnValue('/dashboard/patients')
      render(<Sidebar />)

      const dashboardLink = screen.getByText('Dashboard').closest('a')
      expect(dashboardLink).not.toHaveClass('bg-primary text-primary-foreground')
      expect(dashboardLink).toHaveClass('text-muted-foreground hover:text-foreground hover:bg-accent')
    })
  })

  describe('Logout Functionality', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })
    })

    it('should call logout when logout button is clicked', async () => {
      render(<Sidebar />)

      const logoutButton = screen.getByText('Logout')
      fireEvent.click(logoutButton)

      await waitFor(() => {
        expect(mockLogout).toHaveBeenCalledTimes(1)
      })
    })

    it('should handle logout errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      mockLogout.mockRejectedValue(new Error('Logout failed'))

      render(<Sidebar />)

      const logoutButton = screen.getByText('Logout')
      fireEvent.click(logoutButton)

      await waitFor(() => {
        expect(mockLogout).toHaveBeenCalledTimes(1)
        expect(consoleSpy).toHaveBeenCalledWith('Logout error:', expect.any(Error))
      })

      consoleSpy.mockRestore()
    })
  })

  describe('Component Structure and Styling', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })
    })

    it('should render sidebar header with correct branding', () => {
      render(<Sidebar />)

      expect(screen.getByText('PMS')).toBeInTheDocument()
      expect(screen.getByText('Patient Management')).toBeInTheDocument()
    })

    it('should apply custom className when provided', () => {
      const { container } = render(<Sidebar className="custom-class" />)
      const sidebar = container.firstChild as HTMLElement
      expect(sidebar).toHaveClass('custom-class')
    })

    it('should render navigation icons correctly for admin user', () => {
      render(<Sidebar />)

      expect(screen.getByTestId('home-icon')).toBeInTheDocument()
      expect(screen.getByTestId('users-icon')).toBeInTheDocument()
      expect(screen.getByTestId('stethoscope-icon')).toBeInTheDocument()
      expect(screen.getByTestId('user-plus-icon')).toBeInTheDocument()
      expect(screen.getByTestId('calendar-icon')).toBeInTheDocument()
      expect(screen.queryByTestId('file-text-icon')).not.toBeInTheDocument() // Admin doesn't have Medical Records
      expect(screen.getByTestId('user-icon')).toBeInTheDocument()
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument()
      expect(screen.getByTestId('logout-icon')).toBeInTheDocument()
    })

    it('should render correct href attributes for admin navigation links', () => {
      render(<Sidebar />)

      expect(screen.getByText('Dashboard').closest('a')).toHaveAttribute('href', '/dashboard')
      expect(screen.getByText('Patients').closest('a')).toHaveAttribute('href', '/dashboard/patients')
      expect(screen.getByText('Doctors').closest('a')).toHaveAttribute('href', '/dashboard/doctors')
      expect(screen.getByText('Invite Doctor').closest('a')).toHaveAttribute('href', '/dashboard/invite-doctor')
      expect(screen.getByText('Appointments').closest('a')).toHaveAttribute('href', '/dashboard/appointments')
      expect(screen.getByText('Profile').closest('a')).toHaveAttribute('href', '/dashboard/profile')
      expect(screen.getByText('Settings').closest('a')).toHaveAttribute('href', '/dashboard/settings')
    })

    it('should render Medical Records for doctor user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '2', email: '<EMAIL>', role: 'doctor' },
        logout: mockLogout,
        loading: false,
        error: null,
        login: jest.fn(),
        register: jest.fn(),
        registerWithInvitation: jest.fn(),
        loginWithGoogle: jest.fn(),
        clearError: jest.fn(),
        hasRole: jest.fn(),
        hasAnyRole: jest.fn(),
        canAccess: jest.fn(),
        isAdmin: jest.fn(),
        isDoctor: jest.fn(),
        isPatient: jest.fn(),
        isAuthenticated: jest.fn(),
      })

      render(<Sidebar />)

      expect(screen.getByText('Medical Records')).toBeInTheDocument()
      expect(screen.getByTestId('file-text-icon')).toBeInTheDocument()
      expect(screen.getByText('Medical Records').closest('a')).toHaveAttribute('href', '/dashboard/medical-records')
    })
  })
})
