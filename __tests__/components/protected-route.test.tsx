import { render, screen } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { ProtectedRoute } from '../../components/auth/protected-route'
import { useAuth } from '../../lib/hooks'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock useAuth hook
jest.mock('../../lib/hooks', () => ({
  useAuth: jest.fn(),
}))

const mockPush = jest.fn()
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('ProtectedRoute Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    })
  })

  it('should show loading state when auth is loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: true,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(screen.getByText('Checking authentication...')).toBeInTheDocument()
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
  })

  it('should redirect to login when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(mockPush).toHaveBeenCalledWith('/login')
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
  })

  it('should render children when user is authenticated with correct role', () => {
    const mockUser = {
      uid: '123',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: 'admin' as const,
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(
      <ProtectedRoute allowedRoles={['admin']}>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(screen.getByText('Protected Content')).toBeInTheDocument()
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should redirect when user does not have required role', () => {
    const mockUser = {
      uid: '123',
      email: '<EMAIL>',
      displayName: 'Patient User',
      role: 'patient' as const,
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(
      <ProtectedRoute allowedRoles={['admin']}>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(mockPush).toHaveBeenCalledWith('/dashboard/patient')
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
  })

  it('should allow access when no specific roles are required', () => {
    const mockUser = {
      uid: '123',
      email: '<EMAIL>',
      displayName: 'Any User',
      role: 'patient' as const,
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(screen.getByText('Protected Content')).toBeInTheDocument()
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should use custom redirect path', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      error: null,
      login: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      clearError: jest.fn(),
      hasRole: jest.fn(),
      canAccess: jest.fn(),
      isAdmin: jest.fn(),
      isDoctor: jest.fn(),
      isPatient: jest.fn(),
    })

    render(
      <ProtectedRoute redirectTo="/custom-login">
        <div>Protected Content</div>
      </ProtectedRoute>
    )

    expect(mockPush).toHaveBeenCalledWith('/custom-login')
  })
})
