import '@testing-library/jest-dom'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import { AuthProvider } from '../lib/contexts/auth.context'
import { useAuth } from '../lib/hooks/use-auth'
import { User, UserRole } from '../lib/types'

// Mock the auth service to return a mock adapter
const mockAuthAdapter = {
  login: jest.fn(),
  register: jest.fn(),
  registerWithInvitation: jest.fn(),
  loginWithGoogle: jest.fn(),
  logout: jest.fn(),
  getCurrentUser: jest.fn(),
  onAuthStateChange: jest.fn(),
}

jest.mock('../lib/services/auth.service', () => ({
  getAuthAdapter: jest.fn(() => mockAuthAdapter)
}))

// Mock the roles constants
jest.mock('../lib/constants/roles', () => ({
  hasPermission: jest.fn((role: User<PERSON>ole, permission: string) => {
    const permissions: Record<UserRole, string[]> = {
      admin: ['manage_users', 'invite_doctors', 'view_all_patients'],
      doctor: ['view_assigned_patients', 'manage_patient_records'],
      patient: ['view_own_records', 'view_appointments']
    }
    return permissions[role]?.includes(permission) || false
  })
}))

// Import the mocked service (for type checking)
// import { getAuthAdapter } from '../lib/services/auth.service'
// import { hasPermission } from '../lib/constants/roles'

// Test component to access auth context
const TestComponent = () => {
  const { user, login, logout, loading, error, register, loginWithGoogle, clearError } = useAuth()

  const handleLogin = async () => {
    try {
      await login({ email: '<EMAIL>', password: 'password' })
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  const handleRegister = async () => {
    try {
      await register({
        email: '<EMAIL>',
        password: 'password',
        displayName: 'New User',
        role: 'patient'
      })
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  const handleRegisterWithInvitation = async () => {
    try {
      await register({
        email: '<EMAIL>',
        password: 'password',
        displayName: 'Doctor User',
        role: 'doctor'
      }, 'invitation-token-123')
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  const handleGoogleLogin = async () => {
    try {
      await loginWithGoogle()
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  return (
    <div>
      <div data-testid="user-info">
        {user ? `${user.email} - ${user.role}` : 'No user'}
      </div>
      <div data-testid="loading">{loading ? 'Loading' : 'Not loading'}</div>
      <div data-testid="error">{error || 'No error'}</div>
      <button data-testid="login-btn" onClick={handleLogin}>Login</button>
      <button data-testid="register-btn" onClick={handleRegister}>Register</button>
      <button data-testid="register-invitation-btn" onClick={handleRegisterWithInvitation}>Register with Invitation</button>
      <button data-testid="google-login-btn" onClick={handleGoogleLogin}>Google Login</button>
      <button data-testid="logout-btn" onClick={handleLogout}>Logout</button>
      <button data-testid="clear-error-btn" onClick={clearError}>Clear Error</button>
    </div>
  )
}

const renderWithAuthProvider = (component: React.ReactElement<any>) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  )
}

describe('Authentication Context', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset mock implementations
    mockAuthAdapter.getCurrentUser.mockResolvedValue(null)
    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      // Immediately call with null user to simulate no user logged in
      callback(null)
      return () => {} // Return unsubscribe function
    })
  })

  it('should render initial state correctly', () => {
    renderWithAuthProvider(<TestComponent />)

    expect(screen.getByTestId('user-info')).toHaveTextContent('No user')
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')
    expect(screen.getByTestId('error')).toHaveTextContent('No error')
  })

  it('should handle successful login', async () => {
    const mockUser: User = {
      uid: '123',
      email: '<EMAIL>',
      displayName: 'Test User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.login.mockResolvedValue(mockUser)

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('login-btn'))

    await waitFor(() => {
      expect(mockAuthAdapter.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password'
      })
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('<EMAIL> - patient')
    })
  })

  it('should handle login error', async () => {
    const errorMessage = 'Invalid credentials'
    mockAuthAdapter.login.mockRejectedValue(new Error(errorMessage))

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('login-btn'))

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent(errorMessage)
    })
  })

  it('should handle successful registration without invitation', async () => {
    const mockUser: User = {
      uid: '456',
      email: '<EMAIL>',
      displayName: 'New User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.register.mockResolvedValue(mockUser)

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('register-btn'))

    await waitFor(() => {
      expect(mockAuthAdapter.register).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
        displayName: 'New User',
        role: 'patient'
      })
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('<EMAIL> - patient')
    })
  })

  it('should handle successful registration with invitation', async () => {
    const mockUser: User = {
      uid: '789',
      email: '<EMAIL>',
      displayName: 'Doctor User',
      role: 'doctor',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.registerWithInvitation.mockResolvedValue(mockUser)

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('register-invitation-btn'))

    await waitFor(() => {
      expect(mockAuthAdapter.registerWithInvitation).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
        displayName: 'Doctor User',
        role: 'doctor'
      }, 'invitation-token-123')
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('<EMAIL> - doctor')
    })
  })

  it('should handle registration error', async () => {
    const errorMessage = 'Registration failed'
    mockAuthAdapter.register.mockRejectedValue(new Error(errorMessage))

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('register-btn'))

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent(errorMessage)
    })
  })

  it('should handle Google login success', async () => {
    const mockUser: User = {
      uid: 'google-123',
      email: '<EMAIL>',
      displayName: 'Google User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.loginWithGoogle.mockResolvedValue(mockUser)

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('google-login-btn'))

    await waitFor(() => {
      expect(mockAuthAdapter.loginWithGoogle).toHaveBeenCalled()
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('<EMAIL> - patient')
    })
  })

  it('should handle Google login error', async () => {
    const errorMessage = 'Google login failed'
    mockAuthAdapter.loginWithGoogle.mockRejectedValue(new Error(errorMessage))

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('google-login-btn'))

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent(errorMessage)
    })
  })

  it('should handle logout', async () => {
    mockAuthAdapter.logout.mockResolvedValue(undefined)

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('logout-btn'))

    await waitFor(() => {
      expect(mockAuthAdapter.logout).toHaveBeenCalled()
    })
  })

  it('should handle logout error', async () => {
    const errorMessage = 'Logout failed'
    mockAuthAdapter.logout.mockRejectedValue(new Error(errorMessage))

    renderWithAuthProvider(<TestComponent />)

    fireEvent.click(screen.getByTestId('logout-btn'))

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent(errorMessage)
    })
  })

  it('should clear error when clearError is called', async () => {
    const errorMessage = 'Test error'
    mockAuthAdapter.login.mockRejectedValue(new Error(errorMessage))

    renderWithAuthProvider(<TestComponent />)

    // Trigger an error
    fireEvent.click(screen.getByTestId('login-btn'))

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent(errorMessage)
    })

    // Clear the error
    fireEvent.click(screen.getByTestId('clear-error-btn'))

    expect(screen.getByTestId('error')).toHaveTextContent('No error')
  })

  it('should handle auth state changes', async () => {
    let authCallback: ((user: User | null) => void) | null = null

    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      authCallback = callback
      return () => {} // Return unsubscribe function
    })

    renderWithAuthProvider(<TestComponent />)

    // Initially no user
    expect(screen.getByTestId('user-info')).toHaveTextContent('No user')

    // Simulate user login via auth state change
    const mockUser: User = {
      uid: 'state-change-123',
      email: '<EMAIL>',
      displayName: 'State Change User',
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    act(() => {
      authCallback?.(mockUser)
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('<EMAIL> - admin')
    })

    // Simulate user logout via auth state change
    act(() => {
      authCallback?.(null)
    })

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('No user')
    })
  })

  it('should show loading state during operations', async () => {
    let resolveLogin: (value: User) => void
    const loginPromise = new Promise<User>((resolve) => {
      resolveLogin = resolve
    })

    mockAuthAdapter.login.mockReturnValue(loginPromise)

    renderWithAuthProvider(<TestComponent />)

    // Initially not loading
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')

    // Start login
    fireEvent.click(screen.getByTestId('login-btn'))

    // Should show loading
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Loading')
    })

    // Complete login
    const mockUser: User = {
      uid: 'loading-test',
      email: '<EMAIL>',
      displayName: 'Loading User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    act(() => {
      resolveLogin(mockUser)
    })

    // Should stop loading
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')
    })
  })
})

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      callback(null)
      return () => {}
    })
  })

  it('should provide role-based utility functions when no user is logged in', () => {
    const TestRoleComponent = () => {
      const { hasRole, canAccess, isAdmin, isDoctor, isPatient, isAuthenticated } = useAuth()

      return (
        <div>
          <div data-testid="has-admin">{hasRole('admin').toString()}</div>
          <div data-testid="can-access-manage-users">{canAccess('manage_users').toString()}</div>
          <div data-testid="is-admin">{isAdmin().toString()}</div>
          <div data-testid="is-doctor">{isDoctor().toString()}</div>
          <div data-testid="is-patient">{isPatient().toString()}</div>
          <div data-testid="is-authenticated">{isAuthenticated().toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestRoleComponent />)

    // When no user is logged in, all should be false
    expect(screen.getByTestId('has-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-manage-users')).toHaveTextContent('false')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('is-doctor')).toHaveTextContent('false')
    expect(screen.getByTestId('is-patient')).toHaveTextContent('false')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false')
  })

  it('should provide correct role-based functions for admin user', () => {
    const adminUser: User = {
      uid: 'admin-123',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      callback(adminUser)
      return () => {}
    })

    const TestRoleComponent = () => {
      const { hasRole, canAccess, isAdmin, isDoctor, isPatient, isAuthenticated, hasAnyRole } = useAuth()

      return (
        <div>
          <div data-testid="has-admin">{hasRole('admin').toString()}</div>
          <div data-testid="has-doctor">{hasRole('doctor').toString()}</div>
          <div data-testid="has-patient">{hasRole('patient').toString()}</div>
          <div data-testid="can-access-manage-users">{canAccess('manage_users').toString()}</div>
          <div data-testid="can-access-view-records">{canAccess('view_own_records').toString()}</div>
          <div data-testid="is-admin">{isAdmin().toString()}</div>
          <div data-testid="is-doctor">{isDoctor().toString()}</div>
          <div data-testid="is-patient">{isPatient().toString()}</div>
          <div data-testid="is-authenticated">{isAuthenticated().toString()}</div>
          <div data-testid="has-any-admin-doctor">{hasAnyRole(['admin', 'doctor']).toString()}</div>
          <div data-testid="has-any-patient">{hasAnyRole(['patient']).toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestRoleComponent />)

    // Admin user should have admin role and permissions
    expect(screen.getByTestId('has-admin')).toHaveTextContent('true')
    expect(screen.getByTestId('has-doctor')).toHaveTextContent('false')
    expect(screen.getByTestId('has-patient')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-manage-users')).toHaveTextContent('true')
    expect(screen.getByTestId('can-access-view-records')).toHaveTextContent('false')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('true')
    expect(screen.getByTestId('is-doctor')).toHaveTextContent('false')
    expect(screen.getByTestId('is-patient')).toHaveTextContent('false')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true')
    expect(screen.getByTestId('has-any-admin-doctor')).toHaveTextContent('true')
    expect(screen.getByTestId('has-any-patient')).toHaveTextContent('false')
  })

  it('should provide correct role-based functions for doctor user', () => {
    const doctorUser: User = {
      uid: 'doctor-123',
      email: '<EMAIL>',
      displayName: 'Doctor User',
      role: 'doctor',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      callback(doctorUser)
      return () => {}
    })

    const TestRoleComponent = () => {
      const { hasRole, canAccess, isAdmin, isDoctor, isPatient, isAuthenticated, hasAnyRole } = useAuth()

      return (
        <div>
          <div data-testid="has-admin">{hasRole('admin').toString()}</div>
          <div data-testid="has-doctor">{hasRole('doctor').toString()}</div>
          <div data-testid="has-patient">{hasRole('patient').toString()}</div>
          <div data-testid="can-access-manage-users">{canAccess('manage_users').toString()}</div>
          <div data-testid="can-access-patient-records">{canAccess('manage_patient_records').toString()}</div>
          <div data-testid="is-admin">{isAdmin().toString()}</div>
          <div data-testid="is-doctor">{isDoctor().toString()}</div>
          <div data-testid="is-patient">{isPatient().toString()}</div>
          <div data-testid="is-authenticated">{isAuthenticated().toString()}</div>
          <div data-testid="has-any-admin-doctor">{hasAnyRole(['admin', 'doctor']).toString()}</div>
          <div data-testid="has-any-patient">{hasAnyRole(['patient']).toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestRoleComponent />)

    // Doctor user should have doctor role and permissions
    expect(screen.getByTestId('has-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('has-doctor')).toHaveTextContent('true')
    expect(screen.getByTestId('has-patient')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-manage-users')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-patient-records')).toHaveTextContent('true')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('is-doctor')).toHaveTextContent('true')
    expect(screen.getByTestId('is-patient')).toHaveTextContent('false')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true')
    expect(screen.getByTestId('has-any-admin-doctor')).toHaveTextContent('true')
    expect(screen.getByTestId('has-any-patient')).toHaveTextContent('false')
  })

  it('should provide correct role-based functions for patient user', () => {
    const patientUser: User = {
      uid: 'patient-123',
      email: '<EMAIL>',
      displayName: 'Patient User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      callback(patientUser)
      return () => {}
    })

    const TestRoleComponent = () => {
      const { hasRole, canAccess, isAdmin, isDoctor, isPatient, isAuthenticated, hasAnyRole } = useAuth()

      return (
        <div>
          <div data-testid="has-admin">{hasRole('admin').toString()}</div>
          <div data-testid="has-doctor">{hasRole('doctor').toString()}</div>
          <div data-testid="has-patient">{hasRole('patient').toString()}</div>
          <div data-testid="can-access-manage-users">{canAccess('manage_users').toString()}</div>
          <div data-testid="can-access-own-records">{canAccess('view_own_records').toString()}</div>
          <div data-testid="is-admin">{isAdmin().toString()}</div>
          <div data-testid="is-doctor">{isDoctor().toString()}</div>
          <div data-testid="is-patient">{isPatient().toString()}</div>
          <div data-testid="is-authenticated">{isAuthenticated().toString()}</div>
          <div data-testid="has-any-admin-doctor">{hasAnyRole(['admin', 'doctor']).toString()}</div>
          <div data-testid="has-any-patient">{hasAnyRole(['patient']).toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestRoleComponent />)

    // Patient user should have patient role and permissions
    expect(screen.getByTestId('has-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('has-doctor')).toHaveTextContent('false')
    expect(screen.getByTestId('has-patient')).toHaveTextContent('true')
    expect(screen.getByTestId('can-access-manage-users')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-own-records')).toHaveTextContent('true')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('is-doctor')).toHaveTextContent('false')
    expect(screen.getByTestId('is-patient')).toHaveTextContent('true')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true')
    expect(screen.getByTestId('has-any-admin-doctor')).toHaveTextContent('false')
    expect(screen.getByTestId('has-any-patient')).toHaveTextContent('true')
  })

  it('should handle loading state correctly in isAuthenticated', () => {
    const TestLoadingComponent = () => {
      const { isAuthenticated, loading } = useAuth()

      return (
        <div>
          <div data-testid="loading">{loading.toString()}</div>
          <div data-testid="is-authenticated">{isAuthenticated().toString()}</div>
        </div>
      )
    }

    // Mock loading state
    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      // Don't call callback immediately to simulate loading
      return () => {}
    })

    renderWithAuthProvider(<TestLoadingComponent />)

    // When loading, isAuthenticated should be false even if user might exist
    expect(screen.getByTestId('loading')).toHaveTextContent('true')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false')
  })

  it('should handle hasAnyRole with empty array', () => {
    const patientUser: User = {
      uid: 'patient-123',
      email: '<EMAIL>',
      displayName: 'Patient User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      callback(patientUser)
      return () => {}
    })

    const TestRoleComponent = () => {
      const { hasAnyRole } = useAuth()

      return (
        <div>
          <div data-testid="has-any-empty">{hasAnyRole([]).toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestRoleComponent />)

    // Empty array should return false
    expect(screen.getByTestId('has-any-empty')).toHaveTextContent('false')
  })

  it('should handle canAccess with non-existent permission', () => {
    const adminUser: User = {
      uid: 'admin-123',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    mockAuthAdapter.onAuthStateChange.mockImplementation((callback) => {
      callback(adminUser)
      return () => {}
    })

    const TestRoleComponent = () => {
      const { canAccess } = useAuth()

      return (
        <div>
          <div data-testid="can-access-nonexistent">{canAccess('nonexistent_permission').toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestRoleComponent />)

    // Non-existent permission should return false
    expect(screen.getByTestId('can-access-nonexistent')).toHaveTextContent('false')
  })

  it('should throw error when useAuth is used outside AuthProvider', () => {
    const TestComponentOutsideProvider = () => {
      const auth = useAuth()
      return <div>{auth.user?.email}</div>
    }

    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    expect(() => {
      render(<TestComponentOutsideProvider />)
    }).toThrow('useAuth must be used within an AuthProvider')

    consoleSpy.mockRestore()
  })

  it('should provide all auth context properties through useAuth', () => {
    const TestAllPropsComponent = () => {
      const auth = useAuth()

      return (
        <div>
          <div data-testid="has-user">{(auth.user !== undefined).toString()}</div>
          <div data-testid="has-loading">{(auth.loading !== undefined).toString()}</div>
          <div data-testid="has-error">{(auth.error !== undefined).toString()}</div>
          <div data-testid="has-login">{(typeof auth.login === 'function').toString()}</div>
          <div data-testid="has-register">{(typeof auth.register === 'function').toString()}</div>
          <div data-testid="has-login-google">{(typeof auth.loginWithGoogle === 'function').toString()}</div>
          <div data-testid="has-logout">{(typeof auth.logout === 'function').toString()}</div>
          <div data-testid="has-clear-error">{(typeof auth.clearError === 'function').toString()}</div>
          <div data-testid="has-role-functions">{(typeof auth.hasRole === 'function').toString()}</div>
          <div data-testid="has-any-role">{(typeof auth.hasAnyRole === 'function').toString()}</div>
          <div data-testid="has-can-access">{(typeof auth.canAccess === 'function').toString()}</div>
          <div data-testid="has-is-admin">{(typeof auth.isAdmin === 'function').toString()}</div>
          <div data-testid="has-is-doctor">{(typeof auth.isDoctor === 'function').toString()}</div>
          <div data-testid="has-is-patient">{(typeof auth.isPatient === 'function').toString()}</div>
          <div data-testid="has-is-authenticated">{(typeof auth.isAuthenticated === 'function').toString()}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestAllPropsComponent />)

    // All properties should be available
    expect(screen.getByTestId('has-user')).toHaveTextContent('true')
    expect(screen.getByTestId('has-loading')).toHaveTextContent('true')
    expect(screen.getByTestId('has-error')).toHaveTextContent('true')
    expect(screen.getByTestId('has-login')).toHaveTextContent('true')
    expect(screen.getByTestId('has-register')).toHaveTextContent('true')
    expect(screen.getByTestId('has-login-google')).toHaveTextContent('true')
    expect(screen.getByTestId('has-logout')).toHaveTextContent('true')
    expect(screen.getByTestId('has-clear-error')).toHaveTextContent('true')
    expect(screen.getByTestId('has-role-functions')).toHaveTextContent('true')
    expect(screen.getByTestId('has-any-role')).toHaveTextContent('true')
    expect(screen.getByTestId('has-can-access')).toHaveTextContent('true')
    expect(screen.getByTestId('has-is-admin')).toHaveTextContent('true')
    expect(screen.getByTestId('has-is-doctor')).toHaveTextContent('true')
    expect(screen.getByTestId('has-is-patient')).toHaveTextContent('true')
    expect(screen.getByTestId('has-is-authenticated')).toHaveTextContent('true')
  })
})
