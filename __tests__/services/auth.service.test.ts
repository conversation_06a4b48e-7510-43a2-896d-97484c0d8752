import { AuthAdapter, AuthProvider, AuthConfig } from '../../lib/services/auth/types'
import { FirebaseAuthAdapter } from '../../lib/services/auth/firebase.adapter'
import { getAuthConfig, getAuthAdapter, resetAuthAdapter } from '../../lib/services/auth.service'

// Mock Firebase configuration
jest.mock('../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signInWithPopup: jest.fn(),
  GoogleAuthProvider: jest.fn(),
  signOut: jest.fn(),
  onAuthStateChanged: jest.fn(),
}))

// Mock the Firebase adapter
jest.mock('../../lib/services/auth/firebase.adapter')
const MockedFirebaseAuthAdapter = FirebaseAuthAdapter as jest.MockedClass<typeof FirebaseAuthAdapter>

// Mock console methods
const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()

describe('Auth Service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    resetAuthAdapter()
    delete process.env.AUTH_PROVIDER
    
    // Setup default mock implementation
    MockedFirebaseAuthAdapter.mockImplementation(() => ({
      login: jest.fn(),
      register: jest.fn(),
      registerWithInvitation: jest.fn(),
      loginWithGoogle: jest.fn(),
      logout: jest.fn(),
      getCurrentUser: jest.fn(),
      onAuthStateChange: jest.fn(),
      updateUserRole: jest.fn(),
    } as any))
  })

  afterAll(() => {
    consoleSpy.mockRestore()
  })

  describe('getAuthAdapter', () => {
    it('should create Firebase adapter by default', () => {
      delete process.env.AUTH_PROVIDER
      
      const adapter = getAuthAdapter()
      
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('login')
      expect(adapter).toHaveProperty('register')
      expect(adapter).toHaveProperty('loginWithGoogle')
    })

    it('should create Firebase adapter when AUTH_PROVIDER is firebase', () => {
      process.env.AUTH_PROVIDER = 'firebase'
      
      const adapter = getAuthAdapter()
      
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('login')
      expect(adapter).toHaveProperty('register')
    })

    it('should throw error for auth0 provider (not implemented)', () => {
      process.env.AUTH_PROVIDER = 'auth0'
      
      expect(() => getAuthAdapter()).toThrow('Auth0 adapter not implemented yet')
    })

    it('should throw error for supabase provider (not implemented)', () => {
      process.env.AUTH_PROVIDER = 'supabase'
      
      expect(() => getAuthAdapter()).toThrow('Supabase adapter not implemented yet')
    })

    it('should fall back to Firebase adapter for unknown provider', () => {
      process.env.AUTH_PROVIDER = 'unknown'
      
      const adapter = getAuthAdapter()
      
      expect(consoleSpy).toHaveBeenCalledWith('Unknown auth provider: unknown. Falling back to Firebase.')
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('login')
    })

    it('should return the same instance on subsequent calls (singleton)', () => {
      const adapter1 = getAuthAdapter()
      const adapter2 = getAuthAdapter()
      
      expect(adapter1).toBe(adapter2)
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(1)
    })

    it('should create new instance after reset', () => {
      const adapter1 = getAuthAdapter()
      resetAuthAdapter()
      const adapter2 = getAuthAdapter()
      
      expect(adapter1).not.toBe(adapter2)
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(2)
    })
  })

  describe('getAuthConfig', () => {
    it('should return Firebase config by default', () => {
      delete process.env.AUTH_PROVIDER
      
      const config = getAuthConfig()
      
      expect(config.provider).toBe('firebase')
      expect(config.firebaseConfig).toBeDefined()
      expect(config.auth0Config).toBeUndefined()
      expect(config.supabaseConfig).toBeUndefined()
    })

    it('should return Firebase config when AUTH_PROVIDER is firebase', () => {
      process.env.AUTH_PROVIDER = 'firebase'
      
      const config = getAuthConfig()
      
      expect(config.provider).toBe('firebase')
      expect(config.firebaseConfig).toBeDefined()
    })

    it('should return Auth0 config when AUTH_PROVIDER is auth0', () => {
      process.env.AUTH_PROVIDER = 'auth0'
      process.env.AUTH0_DOMAIN = 'test.auth0.com'
      process.env.AUTH0_CLIENT_ID = 'test-client-id'
      process.env.AUTH0_CLIENT_SECRET = 'test-client-secret'
      
      const config = getAuthConfig()
      
      expect(config.provider).toBe('auth0')
      expect(config.auth0Config).toEqual({
        domain: 'test.auth0.com',
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
      })
      expect(config.firebaseConfig).toBeUndefined()
    })

    it('should return Supabase config when AUTH_PROVIDER is supabase', () => {
      process.env.AUTH_PROVIDER = 'supabase'
      process.env.SUPABASE_URL = 'https://test.supabase.co'
      process.env.SUPABASE_ANON_KEY = 'test-anon-key'
      
      const config = getAuthConfig()
      
      expect(config.provider).toBe('supabase')
      expect(config.supabaseConfig).toEqual({
        url: 'https://test.supabase.co',
        anonKey: 'test-anon-key',
      })
      expect(config.firebaseConfig).toBeUndefined()
    })

    it('should handle missing environment variables gracefully', () => {
      process.env.AUTH_PROVIDER = 'auth0'
      delete process.env.AUTH0_DOMAIN
      delete process.env.AUTH0_CLIENT_ID
      delete process.env.AUTH0_CLIENT_SECRET
      
      const config = getAuthConfig()
      
      expect(config.auth0Config).toEqual({
        domain: undefined,
        clientId: undefined,
        clientSecret: undefined,
      })
    })
  })

  describe('resetAuthAdapter', () => {
    it('should reset the singleton instance', () => {
      const adapter1 = getAuthAdapter()
      resetAuthAdapter()
      const adapter2 = getAuthAdapter()
      
      expect(adapter1).not.toBe(adapter2)
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(2)
    })

    it('should allow switching adapter types after reset', () => {
      // First get Firebase adapter
      process.env.AUTH_PROVIDER = 'firebase'
      const firebaseAdapter = getAuthAdapter()
      expect(MockedFirebaseAuthAdapter).toHaveBeenCalledTimes(1)
      
      // Reset and try to get Auth0 adapter (should throw)
      resetAuthAdapter()
      process.env.AUTH_PROVIDER = 'auth0'
      expect(() => getAuthAdapter()).toThrow('Auth0 adapter not implemented yet')
    })
  })

  describe('Environment-driven Selection', () => {
    it('should respect AUTH_PROVIDER environment variable changes', () => {
      // Test Firebase
      process.env.AUTH_PROVIDER = 'firebase'
      resetAuthAdapter()
      const firebaseAdapter = getAuthAdapter()
      expect(firebaseAdapter).toHaveProperty('login')
      
      // Test unknown provider fallback
      process.env.AUTH_PROVIDER = 'invalid'
      resetAuthAdapter()
      const fallbackAdapter = getAuthAdapter()
      expect(fallbackAdapter).toHaveProperty('login')
      expect(consoleSpy).toHaveBeenCalledWith('Unknown auth provider: invalid. Falling back to Firebase.')
    })

    it('should handle case sensitivity in auth provider', () => {
      process.env.AUTH_PROVIDER = 'FIREBASE'
      
      const adapter = getAuthAdapter()
      
      expect(consoleSpy).toHaveBeenCalledWith('Unknown auth provider: FIREBASE. Falling back to Firebase.')
      expect(adapter).toHaveProperty('login')
    })
  })

  describe('Error Handling', () => {
    it('should handle adapter instantiation errors gracefully', () => {
      MockedFirebaseAuthAdapter.mockImplementation(() => {
        throw new Error('Firebase initialization failed')
      })
      
      expect(() => getAuthAdapter()).toThrow('Firebase initialization failed')
    })
  })
})
