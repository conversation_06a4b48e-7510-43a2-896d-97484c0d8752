import EmailService, { getEmailConfig, getEmailAdapter, resetEmailAdapter } from '../../lib/services/email.service'
import { NodemailerEmailAdapter } from '../../lib/services/email/nodemailer.adapter'
import { Email<PERSON>dapter, EmailProvider, EmailConfig } from '../../lib/services/email/types'

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn(),
    verify: jest.fn(),
  })),
}))

// Mock the Nodemailer adapter
jest.mock('../../lib/services/email/nodemailer.adapter')
const MockedNodemailerEmailAdapter = NodemailerEmailAdapter as jest.MockedClass<typeof NodemailerEmailAdapter>

// Mock console methods
const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()

describe('Email Service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    resetEmailAdapter()
    delete process.env.EMAIL_PROVIDER
    delete process.env.SMTP_HOST
    delete process.env.SMTP_PORT
    delete process.env.SMTP_USER
    delete process.env.SMTP_PASS
    delete process.env.FROM_EMAIL
    delete process.env.FROM_NAME
    
    // Setup default mock implementation
    MockedNodemailerEmailAdapter.mockImplementation(() => ({
      sendEmail: jest.fn(),
      testConnection: jest.fn(),
      sendDoctorInvitation: jest.fn(),
    } as any))
  })

  afterAll(() => {
    consoleSpy.mockRestore()
  })

  describe('getEmailAdapter', () => {
    it('should create Nodemailer adapter by default', () => {
      delete process.env.EMAIL_PROVIDER
      
      const adapter = getEmailAdapter()
      
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('sendEmail')
      expect(adapter).toHaveProperty('testConnection')
      expect(adapter).toHaveProperty('sendDoctorInvitation')
    })

    it('should create Nodemailer adapter when EMAIL_PROVIDER is nodemailer', () => {
      process.env.EMAIL_PROVIDER = 'nodemailer'
      
      const adapter = getEmailAdapter()
      
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('sendEmail')
    })

    it('should throw error for sendgrid provider (not implemented)', () => {
      process.env.EMAIL_PROVIDER = 'sendgrid'
      
      expect(() => getEmailAdapter()).toThrow('SendGrid adapter not implemented yet')
    })

    it('should throw error for ses provider (not implemented)', () => {
      process.env.EMAIL_PROVIDER = 'ses'
      
      expect(() => getEmailAdapter()).toThrow('AWS SES adapter not implemented yet')
    })

    it('should throw error for resend provider (not implemented)', () => {
      process.env.EMAIL_PROVIDER = 'resend'
      
      expect(() => getEmailAdapter()).toThrow('Resend adapter not implemented yet')
    })

    it('should fall back to Nodemailer adapter for unknown provider', () => {
      process.env.EMAIL_PROVIDER = 'unknown'
      
      const adapter = getEmailAdapter()
      
      expect(consoleSpy).toHaveBeenCalledWith('Unknown email provider: unknown. Falling back to Nodemailer.')
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(1)
      expect(adapter).toHaveProperty('sendEmail')
    })

    it('should return the same instance on subsequent calls (singleton)', () => {
      const adapter1 = getEmailAdapter()
      const adapter2 = getEmailAdapter()
      
      expect(adapter1).toBe(adapter2)
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(1)
    })

    it('should create new instance after reset', () => {
      const adapter1 = getEmailAdapter()
      resetEmailAdapter()
      const adapter2 = getEmailAdapter()
      
      expect(adapter1).not.toBe(adapter2)
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(2)
    })
  })

  describe('getEmailConfig', () => {
    it('should return Nodemailer config by default', () => {
      delete process.env.EMAIL_PROVIDER
      
      const config = getEmailConfig()
      
      expect(config.provider).toBe('nodemailer')
      expect(config.nodemailerConfig).toEqual({
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: '',
          pass: '',
        },
      })
      expect(config.sendgridConfig).toBeUndefined()
      expect(config.sesConfig).toBeUndefined()
      expect(config.resendConfig).toBeUndefined()
    })

    it('should return Nodemailer config with custom environment variables', () => {
      process.env.EMAIL_PROVIDER = 'nodemailer'
      process.env.SMTP_HOST = 'smtp.custom.com'
      process.env.SMTP_PORT = '465'
      process.env.SMTP_USER = '<EMAIL>'
      process.env.SMTP_PASS = 'password123'
      
      const config = getEmailConfig()
      
      expect(config.provider).toBe('nodemailer')
      expect(config.nodemailerConfig).toEqual({
        host: 'smtp.custom.com',
        port: 465,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'password123',
        },
      })
    })

    it('should return SendGrid config when EMAIL_PROVIDER is sendgrid', () => {
      process.env.EMAIL_PROVIDER = 'sendgrid'
      process.env.SENDGRID_API_KEY = 'sg-api-key'
      process.env.FROM_EMAIL = '<EMAIL>'
      process.env.FROM_NAME = 'Test System'
      
      const config = getEmailConfig()
      
      expect(config.provider).toBe('sendgrid')
      expect(config.sendgridConfig).toEqual({
        apiKey: 'sg-api-key',
        fromEmail: '<EMAIL>',
        fromName: 'Test System',
      })
      expect(config.nodemailerConfig).toBeUndefined()
    })

    it('should return SES config when EMAIL_PROVIDER is ses', () => {
      process.env.EMAIL_PROVIDER = 'ses'
      process.env.AWS_REGION = 'us-west-2'
      process.env.AWS_ACCESS_KEY_ID = 'access-key'
      process.env.AWS_SECRET_ACCESS_KEY = 'secret-key'
      process.env.FROM_EMAIL = '<EMAIL>'
      
      const config = getEmailConfig()
      
      expect(config.provider).toBe('ses')
      expect(config.sesConfig).toEqual({
        region: 'us-west-2',
        accessKeyId: 'access-key',
        secretAccessKey: 'secret-key',
        fromEmail: '<EMAIL>',
      })
      expect(config.nodemailerConfig).toBeUndefined()
    })

    it('should return Resend config when EMAIL_PROVIDER is resend', () => {
      process.env.EMAIL_PROVIDER = 'resend'
      process.env.RESEND_API_KEY = 'resend-api-key'
      process.env.FROM_EMAIL = '<EMAIL>'
      process.env.FROM_NAME = 'Test System'
      
      const config = getEmailConfig()
      
      expect(config.provider).toBe('resend')
      expect(config.resendConfig).toEqual({
        apiKey: 'resend-api-key',
        fromEmail: '<EMAIL>',
        fromName: 'Test System',
      })
      expect(config.nodemailerConfig).toBeUndefined()
    })

    it('should handle missing environment variables gracefully', () => {
      process.env.EMAIL_PROVIDER = 'sendgrid'
      delete process.env.SENDGRID_API_KEY
      delete process.env.FROM_EMAIL
      delete process.env.FROM_NAME
      
      const config = getEmailConfig()
      
      expect(config.sendgridConfig).toEqual({
        apiKey: '',
        fromEmail: '',
        fromName: 'Patient Management System',
      })
    })

    it('should use default values for SES region', () => {
      process.env.EMAIL_PROVIDER = 'ses'
      delete process.env.AWS_REGION
      
      const config = getEmailConfig()
      
      expect(config.sesConfig?.region).toBe('us-east-1')
    })
  })

  describe('EmailService Class', () => {
    let emailService: EmailService
    let mockAdapter: any

    beforeEach(() => {
      mockAdapter = {
        sendEmail: jest.fn(),
        testConnection: jest.fn(),
        sendDoctorInvitation: jest.fn(),
      }
      MockedNodemailerEmailAdapter.mockImplementation(() => mockAdapter)
      emailService = new EmailService()
    })

    describe('sendEmail', () => {
      it('should delegate to adapter sendEmail method', async () => {
        const emailOptions = {
          to: '<EMAIL>',
          subject: 'Test Subject',
          html: '<p>Test HTML</p>',
          text: 'Test Text',
        }
        mockAdapter.sendEmail.mockResolvedValue(true)
        
        const result = await emailService.sendEmail(emailOptions)
        
        expect(result).toBe(true)
        expect(mockAdapter.sendEmail).toHaveBeenCalledWith(emailOptions)
      })

      it('should handle adapter sendEmail failure', async () => {
        const emailOptions = {
          to: '<EMAIL>',
          subject: 'Test Subject',
        }
        mockAdapter.sendEmail.mockResolvedValue(false)
        
        const result = await emailService.sendEmail(emailOptions)
        
        expect(result).toBe(false)
      })
    })

    describe('testConnection', () => {
      it('should delegate to adapter testConnection method', async () => {
        mockAdapter.testConnection.mockResolvedValue(true)
        
        const result = await emailService.testConnection()
        
        expect(result).toBe(true)
        expect(mockAdapter.testConnection).toHaveBeenCalled()
      })

      it('should handle adapter testConnection failure', async () => {
        mockAdapter.testConnection.mockResolvedValue(false)
        
        const result = await emailService.testConnection()
        
        expect(result).toBe(false)
      })
    })

    describe('sendDoctorInvitation', () => {
      it('should delegate to adapter sendDoctorInvitation method', async () => {
        const email = '<EMAIL>'
        const token = 'invitation-token'
        const invitedByName = 'Admin User'
        mockAdapter.sendDoctorInvitation.mockResolvedValue(true)
        
        const result = await emailService.sendDoctorInvitation(email, token, invitedByName)
        
        expect(result).toBe(true)
        expect(mockAdapter.sendDoctorInvitation).toHaveBeenCalledWith(email, token, invitedByName)
      })

      it('should handle adapter sendDoctorInvitation failure', async () => {
        const email = '<EMAIL>'
        const token = 'invitation-token'
        const invitedByName = 'Admin User'
        mockAdapter.sendDoctorInvitation.mockResolvedValue(false)
        
        const result = await emailService.sendDoctorInvitation(email, token, invitedByName)
        
        expect(result).toBe(false)
      })
    })
  })

  describe('resetEmailAdapter', () => {
    it('should reset the singleton instance', () => {
      const adapter1 = getEmailAdapter()
      resetEmailAdapter()
      const adapter2 = getEmailAdapter()
      
      expect(adapter1).not.toBe(adapter2)
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(2)
    })

    it('should allow switching adapter types after reset', () => {
      // First get Nodemailer adapter
      process.env.EMAIL_PROVIDER = 'nodemailer'
      const nodemailerAdapter = getEmailAdapter()
      expect(MockedNodemailerEmailAdapter).toHaveBeenCalledTimes(1)
      
      // Reset and try to get SendGrid adapter (should throw)
      resetEmailAdapter()
      process.env.EMAIL_PROVIDER = 'sendgrid'
      expect(() => getEmailAdapter()).toThrow('SendGrid adapter not implemented yet')
    })
  })

  describe('Environment-driven Selection', () => {
    it('should respect EMAIL_PROVIDER environment variable changes', () => {
      // Test Nodemailer
      process.env.EMAIL_PROVIDER = 'nodemailer'
      resetEmailAdapter()
      const nodemailerAdapter = getEmailAdapter()
      expect(nodemailerAdapter).toHaveProperty('sendEmail')
      
      // Test unknown provider fallback
      process.env.EMAIL_PROVIDER = 'invalid'
      resetEmailAdapter()
      const fallbackAdapter = getEmailAdapter()
      expect(fallbackAdapter).toHaveProperty('sendEmail')
      expect(consoleSpy).toHaveBeenCalledWith('Unknown email provider: invalid. Falling back to Nodemailer.')
    })

    it('should handle case sensitivity in email provider', () => {
      process.env.EMAIL_PROVIDER = 'NODEMAILER'
      
      const adapter = getEmailAdapter()
      
      expect(consoleSpy).toHaveBeenCalledWith('Unknown email provider: NODEMAILER. Falling back to Nodemailer.')
      expect(adapter).toHaveProperty('sendEmail')
    })
  })

  describe('Error Handling', () => {
    it('should handle adapter instantiation errors gracefully', () => {
      MockedNodemailerEmailAdapter.mockImplementation(() => {
        throw new Error('Nodemailer initialization failed')
      })
      
      expect(() => getEmailAdapter()).toThrow('Nodemailer initialization failed')
    })
  })
})
