import InvitationService from '../../lib/services/invitation.service'
import EmailService from '../../lib/services/email.service'
import { getDatabaseAdapter } from '../../lib/database'
import { User } from '../../lib/types'

// Mock Firebase configuration
jest.mock('../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
}))

// Mock dependencies
jest.mock('../../lib/database')
jest.mock('../../lib/services/email.service')

const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>
const MockedEmailService = EmailService as jest.MockedClass<typeof EmailService>

describe('InvitationService', () => {
  let invitationService: InvitationService
  let mockDataAdapter: any
  let mockEmailService: any
  let mockAdminUser: User
  let mockPatientUser: User

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup mock data adapter
    mockDataAdapter = {
      getUserByFirebaseUid: jest.fn(),
      createDoctorInvitation: jest.fn(),
      getDoctorInvitationByToken: jest.fn(),
      markInvitationAsUsed: jest.fn(),
    }
    mockGetDatabaseAdapter.mockReturnValue(mockDataAdapter)
    
    // Setup mock email service
    mockEmailService = {
      sendDoctorInvitation: jest.fn(),
      testConnection: jest.fn(),
    }
    MockedEmailService.mockImplementation(() => mockEmailService)
    
    // Setup test users
    mockAdminUser = {
      uid: 'admin-123',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    
    mockPatientUser = {
      uid: 'patient-123',
      email: '<EMAIL>',
      displayName: 'Patient User',
      role: 'patient',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    
    invitationService = new InvitationService()
  })

  describe('inviteDoctor', () => {
    it('should successfully invite a doctor', async () => {
      const email = '<EMAIL>'
      const token = 'invitation-token-123'
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)
      mockDataAdapter.createDoctorInvitation.mockResolvedValue({ token, expiresAt })
      mockEmailService.sendDoctorInvitation.mockResolvedValue(true)
      
      const result = await invitationService.inviteDoctor(email, mockAdminUser)
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('Doctor invitation sent successfully')
      expect(result.token).toBe(token)
      expect(result.expiresAt).toBe(expiresAt)
      expect(mockDataAdapter.createDoctorInvitation).toHaveBeenCalledWith(email, mockAdminUser.uid)
      expect(mockEmailService.sendDoctorInvitation).toHaveBeenCalledWith(email, token, mockAdminUser.displayName)
    })

    it('should reject invitation from non-admin user', async () => {
      const email = '<EMAIL>'
      
      const result = await invitationService.inviteDoctor(email, mockPatientUser)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('Only administrators can invite doctors')
      expect(mockDataAdapter.createDoctorInvitation).not.toHaveBeenCalled()
    })

    it('should reject invitation for existing user', async () => {
      const email = '<EMAIL>'
      const existingUser = { ...mockPatientUser, email }
      
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(existingUser)
      
      const result = await invitationService.inviteDoctor(email, mockAdminUser)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('A user with this email already exists')
      expect(mockDataAdapter.createDoctorInvitation).not.toHaveBeenCalled()
    })

    it('should handle email sending failure', async () => {
      const email = '<EMAIL>'
      const token = 'invitation-token-123'
      const expiresAt = new Date()
      
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)
      mockDataAdapter.createDoctorInvitation.mockResolvedValue({ token, expiresAt })
      mockEmailService.sendDoctorInvitation.mockResolvedValue(false)
      
      const result = await invitationService.inviteDoctor(email, mockAdminUser)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('Failed to send invitation email. Please check email configuration.')
    })

    it('should handle database errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const email = '<EMAIL>'
      
      mockDataAdapter.getUserByFirebaseUid.mockRejectedValue(new Error('Database error'))
      
      const result = await invitationService.inviteDoctor(email, mockAdminUser)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('Failed to send invitation. Please try again.')
      
      consoleSpy.mockRestore()
    })
  })

  describe('validateInvitation', () => {
    it('should validate a valid invitation', async () => {
      const token = 'valid-token'
      const invitation = {
        id: 'invitation-123',
        email: '<EMAIL>',
        token,
        isUsed: false,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day from now
        invitedBy: mockAdminUser.uid,
        createdAt: new Date(),
      }
      
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(invitation)
      
      const result = await invitationService.validateInvitation(token)
      
      expect(result.isValid).toBe(true)
      expect(result.message).toBe('Invitation is valid')
      expect(result.invitation).toBe(invitation)
    })

    it('should reject empty token', async () => {
      const result = await invitationService.validateInvitation('')
      
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('Invitation token is required')
      expect(mockDataAdapter.getDoctorInvitationByToken).not.toHaveBeenCalled()
    })

    it('should reject invalid token', async () => {
      const token = 'invalid-token'
      
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(null)
      
      const result = await invitationService.validateInvitation(token)
      
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('Invalid or expired invitation token')
    })

    it('should reject used invitation', async () => {
      const token = 'used-token'
      const invitation = {
        id: 'invitation-123',
        email: '<EMAIL>',
        token,
        isUsed: true,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        invitedBy: mockAdminUser.uid,
        createdAt: new Date(),
      }
      
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(invitation)
      
      const result = await invitationService.validateInvitation(token)
      
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('This invitation has already been used')
    })

    it('should reject expired invitation', async () => {
      const token = 'expired-token'
      const invitation = {
        id: 'invitation-123',
        email: '<EMAIL>',
        token,
        isUsed: false,
        expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        invitedBy: mockAdminUser.uid,
        createdAt: new Date(),
      }
      
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(invitation)
      
      const result = await invitationService.validateInvitation(token)
      
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('This invitation has expired')
    })

    it('should handle database errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const token = 'error-token'
      
      mockDataAdapter.getDoctorInvitationByToken.mockRejectedValue(new Error('Database error'))
      
      const result = await invitationService.validateInvitation(token)
      
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('Failed to validate invitation')
      
      consoleSpy.mockRestore()
    })
  })

  describe('getInvitationEmail', () => {
    it('should return email for valid token', async () => {
      const token = 'valid-token'
      const email = '<EMAIL>'
      const invitation = { email, token }
      
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(invitation)
      
      const result = await invitationService.getInvitationEmail(token)
      
      expect(result).toBe(email)
    })

    it('should return null for invalid token', async () => {
      const token = 'invalid-token'
      
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(null)
      
      const result = await invitationService.getInvitationEmail(token)
      
      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const token = 'error-token'
      
      mockDataAdapter.getDoctorInvitationByToken.mockRejectedValue(new Error('Database error'))
      
      const result = await invitationService.getInvitationEmail(token)
      
      expect(result).toBeNull()
      
      consoleSpy.mockRestore()
    })
  })

  describe('markInvitationAsUsed', () => {
    it('should mark invitation as used successfully', async () => {
      const token = 'valid-token'
      
      mockDataAdapter.markInvitationAsUsed.mockResolvedValue(undefined)
      
      const result = await invitationService.markInvitationAsUsed(token)
      
      expect(result).toBe(true)
      expect(mockDataAdapter.markInvitationAsUsed).toHaveBeenCalledWith(token)
    })

    it('should handle database errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const token = 'error-token'
      
      mockDataAdapter.markInvitationAsUsed.mockRejectedValue(new Error('Database error'))
      
      const result = await invitationService.markInvitationAsUsed(token)
      
      expect(result).toBe(false)
      
      consoleSpy.mockRestore()
    })
  })

  describe('resendInvitation', () => {
    it('should resend invitation successfully', async () => {
      const email = '<EMAIL>'
      
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)
      mockDataAdapter.createDoctorInvitation.mockResolvedValue({ 
        token: 'new-token', 
        expiresAt: new Date() 
      })
      mockEmailService.sendDoctorInvitation.mockResolvedValue(true)
      
      const result = await invitationService.resendInvitation(email, mockAdminUser)
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('Doctor invitation sent successfully')
    })

    it('should reject resend from non-admin user', async () => {
      const email = '<EMAIL>'
      
      const result = await invitationService.resendInvitation(email, mockPatientUser)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('Only administrators can resend invitations')
    })

    it('should handle errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const email = '<EMAIL>'

      mockDataAdapter.getUserByFirebaseUid.mockRejectedValue(new Error('Database error'))

      const result = await invitationService.resendInvitation(email, mockAdminUser)

      expect(result.success).toBe(false)
      // resendInvitation calls inviteDoctor internally, so the error message comes from inviteDoctor
      expect(result.message).toBe('Failed to send invitation. Please try again.')

      consoleSpy.mockRestore()
    })
  })

  describe('testEmailService', () => {
    it('should test email service connection', async () => {
      mockEmailService.testConnection.mockResolvedValue(true)

      const result = await invitationService.testEmailService()

      expect(result).toBe(true)
      expect(mockEmailService.testConnection).toHaveBeenCalled()
    })

    it('should handle email service test failure', async () => {
      mockEmailService.testConnection.mockResolvedValue(false)

      const result = await invitationService.testEmailService()

      expect(result).toBe(false)
    })
  })

  describe('Integration Tests', () => {
    it('should handle complete invitation workflow', async () => {
      const email = '<EMAIL>'
      const token = 'workflow-token'
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

      // Step 1: Invite doctor
      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)
      mockDataAdapter.createDoctorInvitation.mockResolvedValue({ token, expiresAt })
      mockEmailService.sendDoctorInvitation.mockResolvedValue(true)

      const inviteResult = await invitationService.inviteDoctor(email, mockAdminUser)
      expect(inviteResult.success).toBe(true)

      // Step 2: Validate invitation
      const invitation = {
        id: 'invitation-123',
        email,
        token,
        isUsed: false,
        expiresAt,
        invitedBy: mockAdminUser.uid,
        createdAt: new Date(),
      }
      mockDataAdapter.getDoctorInvitationByToken.mockResolvedValue(invitation)

      const validateResult = await invitationService.validateInvitation(token)
      expect(validateResult.isValid).toBe(true)

      // Step 3: Mark as used
      mockDataAdapter.markInvitationAsUsed.mockResolvedValue(undefined)

      const markResult = await invitationService.markInvitationAsUsed(token)
      expect(markResult).toBe(true)
    })

    it('should handle admin role validation across methods', async () => {
      const email = '<EMAIL>'

      // Test invite with patient role
      const inviteResult = await invitationService.inviteDoctor(email, mockPatientUser)
      expect(inviteResult.success).toBe(false)
      expect(inviteResult.message).toBe('Only administrators can invite doctors')

      // Test resend with patient role
      const resendResult = await invitationService.resendInvitation(email, mockPatientUser)
      expect(resendResult.success).toBe(false)
      expect(resendResult.message).toBe('Only administrators can resend invitations')
    })
  })
})
