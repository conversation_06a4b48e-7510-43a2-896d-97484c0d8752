# Patient Management System

A comprehensive healthcare management platform built with Next.js, TypeScript, and Firebase. This system provides role-based access control for administrators, doctors, and patients with a modern, responsive interface.

## 🚀 Features

### 🔐 Authentication & Authorization
- **Firebase Authentication** with email/password and Google OAuth
- **User registration** with role selection (<PERSON><PERSON>, Doctor, Patient)
- **Google Sign-In** integration for quick account creation
- **Role-based access control** (<PERSON><PERSON>, <PERSON>, <PERSON>ient)
- **Protected routes** with middleware
- **Secure session management**

### 👥 User Roles

**Administrator**
- User management and system oversight
- Doctor invitation and management
- System analytics and metrics
- Full access to all patient and doctor data

**Doctor**
- Patient management and medical records
- Appointment scheduling and management
- Medical history access and updates
- Patient communication tools

**Patient**
- Personal medical record access
- Appointment viewing and history
- Profile management
- Medical history tracking

### 🎨 Modern UI/UX
- **Responsive design** with Tailwind CSS
- **Dark/Light theme** support
- **Component-driven architecture**
- **Accessible interface** with proper ARIA labels
- **Loading states** and error handling

### 🏗️ Architecture
- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Adapter pattern** for external services
- **Context-based state management**
- **Modular component structure**

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project (for authentication and database)
- Google OAuth credentials configured in Firebase Console

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd patient-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Fill in your Firebase configuration:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   ```

4. **Set up Firebase**
   - Create a new Firebase project
   - Enable Authentication with Email/Password and Google provider
   - Create Firestore database
   - Add your domain to authorized domains
   - Configure Google OAuth credentials

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

Generate coverage report:
```bash
npm run test:coverage
```

## 🏗️ Build & Deploy

Build for production:
```bash
npm run build
```

Start production server:
```bash
npm start
```

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Protected dashboard pages
│   ├── login/            # Authentication page
│   └── globals.css       # Global styles
├── components/            # Reusable UI components
│   ├── auth/             # Authentication components
│   ├── navigation/       # Navigation components
│   └── ui/               # Base UI components
├── lib/                   # Core business logic
│   ├── adapters/         # External service adapters
│   ├── contexts/         # React contexts
│   ├── hooks/            # Custom hooks
│   ├── types/            # TypeScript definitions
│   ├── constants/        # Application constants
│   └── utils/            # Utility functions
├── __tests__/            # Test files
└── Configuration files
```

## 🔧 Configuration

### Firebase Setup
1. Create a Firebase project
2. Enable Authentication
3. Set up Firestore with the following collections:
   - `users` - User profiles and roles
   - `patients` - Patient information
   - `doctors` - Doctor profiles
   - `appointments` - Appointment data
   - `medical_records` - Medical records

### Environment Variables
All environment variables should be prefixed with `NEXT_PUBLIC_` for client-side access:

- `NEXT_PUBLIC_FIREBASE_API_KEY`
- `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN`
- `NEXT_PUBLIC_FIREBASE_PROJECT_ID`
- `NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET`
- `NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID`
- `NEXT_PUBLIC_FIREBASE_APP_ID`

## 🎯 Demo Accounts

For testing purposes, you can use these demo accounts:

- **Admin**: <EMAIL> / admin123
- **Doctor**: <EMAIL> / doctor123  
- **Patient**: <EMAIL> / patient123

*Note: These are mock accounts for demonstration. In production, implement proper user registration.*

## 🛡️ Security Features

- **Row Level Security** with Firebase rules
- **Protected routes** with authentication middleware
- **Role-based access control** throughout the application
- **Input validation** and sanitization
- **Secure session management**

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the test files for usage examples

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
  - Authentication system
  - Role-based dashboards
  - Basic patient management
  - Responsive UI components
