#!/usr/bin/env tsx

/**
 * Database Seeding Script
 * 
 * This script seeds the database with initial data for development and testing.
 * It uses the adapter pattern to work with any configured database provider.
 */

import { getDatabaseAdapter } from '../lib/database'
import type { UserRole } from '../lib/types'

async function seedDatabase() {
  console.log('🌱 Starting database seeding...')
  
  try {
    const adapter = getDatabaseAdapter()
    
    // Seed admin user
    console.log('👤 Creating admin user...')
    const adminUser = await adapter.createUser({
      firebaseUid: 'admin-seed-uid',
      email: '<EMAIL>',
      displayName: 'System Administrator',
      role: 'admin' as User<PERSON>ole,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    console.log(`✅ Admin user created: ${adminUser.email}`)
    
    // Seed doctor user
    console.log('👨‍⚕️ Creating doctor user...')
    const doctorUser = await adapter.createUser({
      firebaseUid: 'doctor-seed-uid',
      email: '<EMAIL>',
      displayName: 'Dr. <PERSON>',
      role: 'doctor' as Use<PERSON><PERSON><PERSON>,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    console.log(`✅ Doctor user created: ${doctorUser.email}`)
    
    // Seed patient user
    console.log('👤 Creating patient user...')
    const patientUser = await adapter.createUser({
      firebaseUid: 'patient-seed-uid',
      email: '<EMAIL>',
      displayName: 'Jane Doe',
      role: 'patient' as UserRole,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    console.log(`✅ Patient user created: ${patientUser.email}`)
    
    // Create doctor invitation for testing
    console.log('📧 Creating doctor invitation...')
    await adapter.createDoctorInvitation('<EMAIL>', adminUser.uid)
    console.log('✅ Doctor invitation created')
    
    console.log('🎉 Database seeding completed successfully!')
    
    // Display seeded data summary
    console.log('\n📊 Seeded Data Summary:')
    console.log('- Admin User: <EMAIL>')
    console.log('- Doctor User: <EMAIL>')
    console.log('- Patient User: <EMAIL>')
    console.log('- Doctor Invitation: <EMAIL>')
    console.log('\n💡 Use these credentials for testing (with Firebase Auth)')
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    process.exit(1)
  }
}

// Handle script execution
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Seeding script failed:', error)
      process.exit(1)
    })
}

export { seedDatabase }
