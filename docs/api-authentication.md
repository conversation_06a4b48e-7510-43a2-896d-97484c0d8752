# API Authentication and Authorization

This document describes the role-based authentication and authorization system implemented for all API routes in the Patient Management System.

## Overview

The system implements comprehensive role-based access control (RBAC) with three distinct roles:
- **Admin**: Full system access
- **Doctor**: Access to doctor-specific features and assigned patients
- **Patient**: Access only to their own data

## Authentication Middleware

All API routes are protected using authentication middleware located in `lib/middleware/api-auth.ts`.

### Available Middleware Functions

- `with<PERSON><PERSON><PERSON><PERSON>(handler)`: Requires any authenticated user
- `withAdmin<PERSON><PERSON>(handler)`: Requires admin role
- `withDoctorAuth(handler)`: Requires doctor or admin role

### Utility Functions

- `isAdmin(user)`: Check if user has admin role
- `isDoctor(user)`: Check if user has doctor role (includes admin)
- `isPatient(user)`: Check if user has patient role
- `isValidUUID(uuid)`: Validate UUID format
- `createErrorResponse(message, status, code?, details?)`: Create standardized error responses

## API Endpoints and Access Control

### Authentication Routes

#### `POST /api/auth/update-role`
- **Access**: Admin only
- **Purpose**: Update user roles
- **Restrictions**: Users cannot change their own role

#### `GET /api/auth/user`
- **Access**: Any authenticated user
- **Purpose**: Get user information
- **Restrictions**: Users can only access their own data unless they're admin

### Patient Management Routes

#### `GET /api/patients`
- **Access**: Admin and Doctor only
- **Purpose**: List all patients with pagination
- **Restrictions**: Patients cannot access this endpoint

#### `POST /api/patients`
- **Access**: Admin only
- **Purpose**: Create new patient records
- **Validation**: Email format, date of birth, required fields

#### `GET /api/patients/[id]`
- **Access**: Role-based
  - Admin: Can access any patient
  - Doctor: Can access any patient (for collaboration)
  - Patient: Can only access their own record
- **Purpose**: Get specific patient details

#### `PUT /api/patients/[id]`
- **Access**: Admin and Doctor only
- **Purpose**: Update patient information
- **Restrictions**: Only admins can change doctor assignments

#### `DELETE /api/patients/[id]`
- **Access**: Admin only
- **Purpose**: Delete patient records

### Doctor Management Routes

#### `GET /api/doctors`
- **Access**: All authenticated users
- **Purpose**: List all doctors (for appointment booking/collaboration)

#### `POST /api/doctors`
- **Access**: Admin only
- **Purpose**: Create new doctor profiles
- **Validation**: Email format, experience validation, required fields

#### `GET /api/doctors/[doctorId]`
- **Access**: All authenticated users
- **Purpose**: Get doctor details
- **Restrictions**: Patients see limited data (no patient list)

#### `PUT /api/doctors/[doctorId]`
- **Access**: Role-based
  - Admin: Can update any doctor
  - Doctor: Can only update their own profile
- **Purpose**: Update doctor information

#### `DELETE /api/doctors/[doctorId]`
- **Access**: Admin only
- **Purpose**: Delete doctor profiles

### Medical Records Routes (Doctor Notes)

#### `GET /api/medical-records`
- **Access**: Role-based
  - Admin: Can see all medical records
  - Doctor: Can see records they created
  - Patient: Can see their own records (with patientId parameter)
- **Purpose**: List medical records with pagination

#### `POST /api/medical-records`
- **Access**: Doctor and Admin only
- **Purpose**: Create new medical records/notes
- **Validation**: Patient existence, doctor assignment

#### `GET /api/medical-records/[id]`
- **Access**: Role-based
  - Admin: Can access any record
  - Doctor: Can access records they created
  - Patient: Can access their own records
- **Purpose**: Get specific medical record

#### `PUT /api/medical-records/[id]`
- **Access**: Role-based
  - Admin: Can update any record and change assignments
  - Doctor: Can only update records they created
- **Purpose**: Update medical record information

#### `DELETE /api/medical-records/[id]`
- **Access**: Admin only
- **Purpose**: Delete medical records

### Invitation Routes

#### `POST /api/invitations/send`
- **Access**: Admin only
- **Purpose**: Send user invitations

#### `GET /api/invitations/stats`
- **Access**: Admin only
- **Purpose**: Get invitation statistics

## Error Handling

All API routes return standardized error responses with appropriate HTTP status codes:

- `400`: Bad Request (validation errors, invalid data)
- `401`: Unauthorized (missing or invalid authentication)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (resource doesn't exist)
- `500`: Internal Server Error (database or system errors)

### Error Response Format

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "additional": "context"
  }
}
```

## Security Features

1. **JWT Token Validation**: All requests validated using Firebase Authentication
2. **Role-Based Access Control**: Granular permissions based on user roles
3. **Resource-Level Security**: Users can only access authorized resources
4. **Input Validation**: Comprehensive validation of all input data
5. **UUID Validation**: Proper validation of resource identifiers
6. **Audit Logging**: All API access is logged for security monitoring

## Testing

Comprehensive test suites are available for all API routes:
- `__tests__/api/patients/route.test.ts`
- `__tests__/api/patients/[id]/route.test.ts`
- `__tests__/api/doctors/route.test.ts`
- `__tests__/api/medical-records/route.test.ts`

Tests cover:
- Role-based access control
- Input validation
- Error handling
- Database error scenarios
- Edge cases and security boundaries

## Implementation Notes

1. All routes use the adapter pattern for database operations
2. Medical records serve as doctor notes with rich text support
3. Pagination is implemented for list endpoints
4. Consistent error handling across all routes
5. Comprehensive logging for audit trails
6. UUID validation for all resource identifiers

## Future Enhancements

1. Doctor-patient assignment validation
2. Appointment-based access control
3. Time-based access restrictions
4. Advanced audit logging
5. Rate limiting implementation
