'use client'

import React, { useState, useEffect } from 'react'
import { Users, UserPlus, Calendar, FileText, TrendingUp, Activity } from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Badge
} from '../../../components/ui'
import { ProtectedRoute } from '../../../components/auth'
import InviteDoctorModal from '../../../components/admin/invite-doctor-modal'

// Mock data - in a real app, this would come from your data adapter
const mockStats = {
  totalPatients: 156,
  totalDoctors: 12,
  totalAppointments: 89,
  pendingInvitations: 3,
  recentActivity: [
    { id: 1, type: 'patient_registered', message: 'New patient <PERSON> registered', time: '2 hours ago' },
    { id: 2, type: 'doctor_invited', message: 'Dr. <PERSON> invited', time: '4 hours ago' },
    { id: 3, type: 'appointment_scheduled', message: 'Appointment scheduled for Patient #123', time: '6 hours ago' },
    { id: 4, type: 'doctor_accepted', message: 'Dr. <PERSON> accepted invitation', time: '1 day ago' },
  ]
}

const mockRecentUsers = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'patient', status: 'active', joinedAt: '2024-01-15' },
  { id: 2, name: 'Dr. <PERSON> <PERSON>', email: '<EMAIL>', role: 'doctor', status: 'pending', joinedAt: '2024-01-14' },
  { id: 3, name: 'Jane Smith', email: '<EMAIL>', role: 'patient', status: 'active', joinedAt: '2024-01-13' },
  { id: 4, name: 'Dr. Michael Brown', email: '<EMAIL>', role: 'doctor', status: 'active', joinedAt: '2024-01-12' },
]

export default function AdminDashboard() {
  const [stats, setStats] = useState(mockStats)
  const [recentUsers, setRecentUsers] = useState(mockRecentUsers)
  const [loading, setLoading] = useState(false)
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false)

  // In a real app, you would fetch data here
  useEffect(() => {
    // fetchDashboardData()
  }, [])

  const handleInviteSuccess = () => {
    // Refresh stats to update pending invitations count
    setStats(prev => ({
      ...prev,
      pendingInvitations: prev.pendingInvitations + 1
    }))
  }

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    trend 
  }: {
    title: string
    value: number
    description: string
    icon: React.ComponentType<{ className?: string }>
    trend?: { value: number; isPositive: boolean }
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {trend && (
          <div className={`flex items-center text-xs mt-1 ${
            trend.isPositive ? 'text-success-600' : 'text-error-600'
          }`}>
            <TrendingUp className="h-3 w-3 mr-1" />
            {trend.isPositive ? '+' : ''}{trend.value}% from last month
          </div>
        )}
      </CardContent>
    </Card>
  )

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Manage your healthcare system and monitor key metrics
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button onClick={() => setIsInviteModalOpen(true)}>
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Doctor
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Patients"
            value={stats.totalPatients}
            description="Active patient accounts"
            icon={Users}
            trend={{ value: 12, isPositive: true }}
          />
          <StatCard
            title="Total Doctors"
            value={stats.totalDoctors}
            description="Active doctor accounts"
            icon={UserPlus}
            trend={{ value: 8, isPositive: true }}
          />
          <StatCard
            title="Appointments"
            value={stats.totalAppointments}
            description="This month"
            icon={Calendar}
            trend={{ value: 15, isPositive: true }}
          />
          <StatCard
            title="Pending Invitations"
            value={stats.pendingInvitations}
            description="Doctor invitations"
            icon={Activity}
          />
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest system activities and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="h-2 w-2 bg-primary rounded-full mt-2"></div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.message}</p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Users */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Users</CardTitle>
              <CardDescription>
                Newly registered users and invitations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-foreground">
                          {user.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium">{user.name}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant={user.role === 'doctor' ? 'default' : 'secondary'}
                      >
                        {user.role}
                      </Badge>
                      <Badge 
                        variant={user.status === 'active' ? 'default' : 'outline'}
                      >
                        {user.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common administrative tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => setIsInviteModalOpen(true)}
              >
                <UserPlus className="h-6 w-6 mb-2" />
                Invite New Doctor
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Users className="h-6 w-6 mb-2" />
                Manage Users
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <FileText className="h-6 w-6 mb-2" />
                System Reports
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Invite Doctor Modal */}
        <InviteDoctorModal
          isOpen={isInviteModalOpen}
          onClose={() => setIsInviteModalOpen(false)}
          onSuccess={handleInviteSuccess}
        />
      </div>
    </ProtectedRoute>
  )
}
