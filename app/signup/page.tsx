'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '../../lib/hooks'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { UserRole } from '../../lib/types'


interface SignupFormData {
  email: string
  password: string
  confirmPassword: string
  displayName: string
  role: UserRole
}

interface InvitationData {
  isValid: boolean
  email: string
  message: string
}

function SignupForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { register, loginWithGoogle } = useAuth()

  // Check for invitation token in URL
  const invitationToken = searchParams.get('invitation')

  const [formData, setFormData] = useState<SignupFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    displayName: '',
    role: 'patient' // Always default to patient
  })
  const [errors, setErrors] = useState<Partial<SignupFormData>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [generalError, setGeneralError] = useState('')
  const [invitation, setInvitation] = useState<InvitationData | null>(null)
  const [isValidatingInvitation, setIsValidatingInvitation] = useState(false)

  // Validate invitation token on component mount
  useEffect(() => {
    if (invitationToken) {
      validateInvitation(invitationToken)
    }
  }, [invitationToken])

  const validateInvitation = async (token: string) => {
    setIsValidatingInvitation(true)
    try {
      const response = await fetch(`/api/invitations/validate?token=${token}`)
      const validation = await response.json()

      if (validation.isValid && validation.invitation) {
        setInvitation({
          isValid: true,
          email: validation.invitation.email,
          message: 'Valid doctor invitation found'
        })
        // Pre-fill email and set role to doctor
        setFormData(prev => ({
          ...prev,
          email: validation.invitation.email,
          role: 'doctor'
        }))
      } else {
        setInvitation({
          isValid: false,
          email: '',
          message: validation.message
        })
        setGeneralError(validation.message)
      }
    } catch (error) {
      setInvitation({
        isValid: false,
        email: '',
        message: 'Failed to validate invitation'
      })
      setGeneralError('Failed to validate invitation')
    } finally {
      setIsValidatingInvitation(false)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<SignupFormData> = {}

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid'
    }

    // For invitation-based signup, validate email matches invitation
    if (invitationToken && invitation?.isValid && formData.email !== invitation.email) {
      newErrors.email = 'Email must match the invitation'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    if (!formData.displayName) {
      newErrors.displayName = 'Display name is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name as keyof SignupFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
    setGeneralError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setGeneralError('')

    try {
      if (invitationToken && invitation?.isValid) {
        // Use invitation-based registration for doctors
        await register({
          email: formData.email,
          password: formData.password,
          displayName: formData.displayName,
          role: 'doctor' // Force doctor role for invitation-based signup
        }, invitationToken)
      } else {
        // Regular patient signup
        await register({
          email: formData.email,
          password: formData.password,
          displayName: formData.displayName,
          role: 'patient' // Force patient role for regular signup
        })
      }

      // Redirect based on role
      const redirectPath = formData.role === 'doctor' ? '/dashboard/doctor' : '/dashboard/patient'
      router.push(redirectPath)
    } catch (error: any) {
      setGeneralError(error.message || 'Failed to create account')
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignup = async () => {
    setIsGoogleLoading(true)
    setGeneralError('')

    try {
      await loginWithGoogle()
      router.push('/dashboard/patient') // Google users default to patient role
    } catch (error: any) {
      setGeneralError(error.message || 'Failed to sign up with Google')
    } finally {
      setIsGoogleLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
            {invitationToken ? 'Complete Doctor Registration' : 'Create Patient Account'}
          </CardTitle>
          <CardDescription>
            {invitationToken
              ? 'Complete your registration using the doctor invitation'
              : 'Join the Patient Management System as a patient'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isValidatingInvitation && (
            <Alert className="mb-4">
              <AlertDescription>Validating invitation...</AlertDescription>
            </Alert>
          )}

          {invitation && invitation.isValid && (
            <Alert className="mb-4 border-green-200 bg-green-50">
              <AlertDescription className="text-green-800">
                ✅ Valid doctor invitation for: {invitation.email}
              </AlertDescription>
            </Alert>
          )}

          {generalError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{generalError}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="displayName" className="text-sm font-medium">
                Full Name
              </label>
              <Input
                id="displayName"
                name="displayName"
                type="text"
                placeholder="Enter your full name"
                value={formData.displayName}
                onChange={handleInputChange}
                required
              />
              {errors.displayName && (
                <p className="text-sm text-destructive">{errors.displayName}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleInputChange}
                autoComplete="email"
                readOnly={!!(invitationToken && invitation?.isValid)}
                className={invitationToken && invitation?.isValid ? 'bg-muted' : ''}
                required
              />
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">
                Password
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Create a password"
                value={formData.password}
                onChange={handleInputChange}
                autoComplete="new-password"
                required
              />
              {errors.password && (
                <p className="text-sm text-destructive">{errors.password}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="text-sm font-medium">
                Confirm Password
              </label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                autoComplete="new-password"
                required
              />
              {errors.confirmPassword && (
                <p className="text-sm text-destructive">{errors.confirmPassword}</p>
              )}
            </div>

            {/* Role is automatically determined: patient for regular signup, doctor for invitation */}
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                Account Type: <strong>{formData.role === 'doctor' ? 'Doctor' : 'Patient'}</strong>
                {invitationToken ? ' (via invitation)' : ' (default)'}
              </div>
            </div>

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </Button>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              type="button"
              variant="outline"
              className="w-full mt-4"
              onClick={handleGoogleSignup}
              disabled={isGoogleLoading}
            >
              {isGoogleLoading ? 'Signing up...' : (
                <>
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continue with Google
                </>
              )}
            </Button>
          </div>

          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>
              Already have an account?{' '}
              <Link href="/login" className="text-primary hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function SignupPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <SignupForm />
    </Suspense>
  )
}
