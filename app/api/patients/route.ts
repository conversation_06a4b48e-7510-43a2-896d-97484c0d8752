import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../lib/database'
import { with<PERSON><PERSON><PERSON><PERSON>, withAdminAuth, AuthenticatedApiRequest, isAdmin, isDoctor } from '../../../lib/middleware/api-auth'
import { Patient, PaginationParams } from '../../../lib/types'

/**
 * GET /api/patients
 * Get list of patients with role-based access control
 * - Admin: Can see all patients
 * - Doctor: Can see assigned patients (for now, all patients - TODO: implement assignment logic)
 * - Patient: Cannot access this endpoint
 */
async function getPatientsHandler(request: AuthenticatedApiRequest) {
  try {
    const user = request.user

    // Only admins and doctors can list patients
    if (!isAdmin(user) && !isDoctor(user)) {
      return NextResponse.json(
        { error: 'Access denied. Only administrators and doctors can view patient lists.' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc'

    const paginationParams: PaginationParams = {
      page: Math.max(1, page),
      limit: Math.min(100, Math.max(1, limit)), // Limit between 1-100
      sortBy,
      sortOrder
    }

    const dataAdapter = getDatabaseAdapter()
    
    // TODO: For doctors, filter by assigned patients
    // For now, doctors can see all patients
    const result = await dataAdapter.getPatients(paginationParams)

    console.log(`User ${user.uid} (${user.role}) retrieved ${result.data.length} patients`)

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      requestedBy: {
        uid: user.uid,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching patients:', error)
    return NextResponse.json(
      { error: 'Failed to fetch patients' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/patients
 * Create a new patient (Admin only)
 */
async function createPatientHandler(request: AuthenticatedApiRequest) {
  try {
    const user = request.user
    const body = await request.json()

    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      assignedDoctorId
    } = body

    // Validate required fields
    if (!firstName || !lastName || !email || !dateOfBirth) {
      return NextResponse.json(
        { error: 'Missing required fields: firstName, lastName, email, dateOfBirth' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate date of birth
    const dob = new Date(dateOfBirth)
    if (isNaN(dob.getTime()) || dob > new Date()) {
      return NextResponse.json(
        { error: 'Invalid date of birth' },
        { status: 400 }
      )
    }

    const patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'> = {
      firstName,
      lastName,
      email,
      phone: phone || '',
      dateOfBirth: dob,
      gender: gender || 'other',
      address: address || {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: ''
      },
      emergencyContact: emergencyContact || {
        name: '',
        relationship: '',
        phone: ''
      },
      medicalHistory: [],
      appointments: [],
      assignedDoctorId
    }

    const dataAdapter = getDatabaseAdapter()
    const patient = await dataAdapter.createPatient(patientData)

    console.log(`Admin ${user.uid} created patient ${patient.id}`)

    return NextResponse.json({
      success: true,
      data: patient,
      message: 'Patient created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating patient:', error)
    return NextResponse.json(
      { error: 'Failed to create patient' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getPatientsHandler)
export const POST = withAdminAuth(createPatientHandler)
