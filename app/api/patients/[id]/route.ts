import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'
import { 
  withU<PERSON><PERSON><PERSON>, 
  withAd<PERSON><PERSON><PERSON>, 
  AuthenticatedApiRequest, 
  isAdmin, 
  isDoctor,
  isValidUUID,
  createErrorResponse
} from '../../../../lib/middleware/api-auth'
import { Patient } from '../../../../lib/types'

interface RouteContext {
  params: {
    id: string
  }
}

/**
 * Check if user can access specific patient data
 */
async function canAccessPatient(user: any, patientId: string): Promise<boolean> {
  // Admin can access any patient
  if (isAdmin(user)) {
    return true
  }

  // Doctor can access assigned patients (for now, all patients - TODO: implement assignment logic)
  if (isDoctor(user)) {
    return true // TODO: Check if patient is assigned to this doctor
  }

  // Patient can only access their own data
  if (user.role === 'patient') {
    const dataAdapter = getDatabaseAdapter()
    const patient = await dataAdapter.getPatientByUserId(user.uid)
    return patient?.id === patientId
  }

  return false
}

/**
 * GET /api/patients/[id]
 * Get specific patient by ID with role-based access control
 */
async function getPatientHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const patientId = context.params.id

    // Validate UUID format
    if (!isValidUUID(patientId)) {
      return createErrorResponse(
        'Invalid patient ID format',
        400,
        'INVALID_PATIENT_ID'
      )
    }

    // Check access permissions
    const hasAccess = await canAccessPatient(user, patientId)
    if (!hasAccess) {
      return createErrorResponse(
        'Access denied to this patient',
        403,
        'PATIENT_ACCESS_DENIED',
        { patientId, userRole: user.role }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const patient = await dataAdapter.getPatientById(patientId)

    if (!patient) {
      return createErrorResponse(
        'Patient not found',
        404,
        'PATIENT_NOT_FOUND'
      )
    }

    console.log(`User ${user.uid} (${user.role}) accessed patient ${patientId}`)

    return NextResponse.json({
      success: true,
      data: patient,
      requestedBy: {
        uid: user.uid,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching patient:', error)
    return NextResponse.json(
      { error: 'Failed to fetch patient' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/patients/[id]
 * Update patient information (Admin and assigned Doctor only)
 */
async function updatePatientHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const patientId = context.params.id
    const body = await request.json()

    // Validate UUID format
    if (!isValidUUID(patientId)) {
      return createErrorResponse(
        'Invalid patient ID format',
        400,
        'INVALID_PATIENT_ID'
      )
    }

    // Only admins and doctors can update patient information
    if (!isAdmin(user) && !isDoctor(user)) {
      return createErrorResponse(
        'Access denied. Only administrators and doctors can update patient information.',
        403,
        'UPDATE_ACCESS_DENIED'
      )
    }

    // Check if patient exists and user has access
    const hasAccess = await canAccessPatient(user, patientId)
    if (!hasAccess) {
      return createErrorResponse(
        'Access denied to this patient',
        403,
        'PATIENT_ACCESS_DENIED',
        { patientId, userRole: user.role }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const existingPatient = await dataAdapter.getPatientById(patientId)

    if (!existingPatient) {
      return createErrorResponse(
        'Patient not found',
        404,
        'PATIENT_NOT_FOUND'
      )
    }

    // Validate and sanitize update data
    const updateData: Partial<Patient> = {}
    
    if (body.firstName) updateData.firstName = body.firstName
    if (body.lastName) updateData.lastName = body.lastName
    if (body.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(body.email)) {
        return createErrorResponse(
          'Invalid email format',
          400,
          'INVALID_EMAIL'
        )
      }
      updateData.email = body.email
    }
    if (body.phone) updateData.phone = body.phone
    if (body.dateOfBirth) {
      const dob = new Date(body.dateOfBirth)
      if (isNaN(dob.getTime()) || dob > new Date()) {
        return createErrorResponse(
          'Invalid date of birth',
          400,
          'INVALID_DATE_OF_BIRTH'
        )
      }
      updateData.dateOfBirth = dob
    }
    if (body.gender) updateData.gender = body.gender
    if (body.address) updateData.address = body.address
    if (body.emergencyContact) updateData.emergencyContact = body.emergencyContact
    if (body.assignedDoctorId !== undefined) {
      // Only admins can change doctor assignments
      if (!isAdmin(user)) {
        return createErrorResponse(
          'Only administrators can change doctor assignments',
          403,
          'ASSIGNMENT_ACCESS_DENIED'
        )
      }
      updateData.assignedDoctorId = body.assignedDoctorId
    }

    const updatedPatient = await dataAdapter.updatePatient(patientId, updateData)

    console.log(`User ${user.uid} (${user.role}) updated patient ${patientId}`)

    return NextResponse.json({
      success: true,
      data: updatedPatient,
      message: 'Patient updated successfully'
    })

  } catch (error) {
    console.error('Error updating patient:', error)
    return NextResponse.json(
      { error: 'Failed to update patient' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/patients/[id]
 * Delete patient (Admin only)
 */
async function deletePatientHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const patientId = context.params.id

    // Validate UUID format
    if (!isValidUUID(patientId)) {
      return createErrorResponse(
        'Invalid patient ID format',
        400,
        'INVALID_PATIENT_ID'
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const patient = await dataAdapter.getPatientById(patientId)

    if (!patient) {
      return createErrorResponse(
        'Patient not found',
        404,
        'PATIENT_NOT_FOUND'
      )
    }

    await dataAdapter.deletePatient(patientId)

    console.log(`Admin ${user.uid} deleted patient ${patientId}`)

    return NextResponse.json({
      success: true,
      message: 'Patient deleted successfully',
      deletedPatient: {
        id: patientId,
        name: `${patient.firstName} ${patient.lastName}`
      }
    })

  } catch (error) {
    console.error('Error deleting patient:', error)
    return NextResponse.json(
      { error: 'Failed to delete patient' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getPatientHandler)
export const PUT = withUserAuth(updatePatientHandler)
export const DELETE = withAdminAuth(deletePatientHandler)
