import { NextResponse } from 'next/server'
import InvitationService from '../../../../lib/services/invitation.service'
import { withAdminAuth, AuthenticatedApiRequest } from '../../../../lib/middleware/api-auth'

async function sendInvitation<PERSON><PERSON>ler(request: AuthenticatedApiRequest) {
  try {
    const body = await request.json()
    const { email } = body

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Use the authenticated admin user as the inviter
    const inviterUser = request.user

    // Create invitation using the service
    const invitationService = new InvitationService()
    const result = await invitationService.inviteDoctor(email, inviterUser)

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    console.log(`Admin ${inviterUser.uid} sent invitation to ${email}`)

    return NextResponse.json({
      success: true,
      message: 'Doctor invitation sent successfully',
      invitation: {
        email,
        token: result.token,
        expiresAt: result.expiresAt,
        invitedBy: {
          uid: inviterUser.uid,
          email: inviterUser.email,
          displayName: inviterUser.displayName
        }
      }
    })

  } catch (error) {
    console.error('Error sending doctor invitation:', error)
    return NextResponse.json(
      { error: 'Failed to send invitation' },
      { status: 500 }
    )
  }
}

export const POST = withAdminAuth(sendInvitationHandler)
