import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'
import InvitationService from '../../../../lib/services/invitation.service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, invitedByUid, doctorInfo } = body

    // Validate required fields
    if (!email || !invitedByUid) {
      return NextResponse.json(
        { error: 'Email and inviter ID are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Get the inviter user to validate permissions
    const dataAdapter = getDatabaseAdapter()
    const inviterUser = await dataAdapter.getUserById(invitedByUid)

    if (!inviterUser) {
      return NextResponse.json(
        { error: 'Inviter not found' },
        { status: 404 }
      )
    }

    // Validate that the inviter is an admin
    if (inviterUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can invite doctors' },
        { status: 403 }
      )
    }

    // Create invitation using the service
    const invitationService = new InvitationService()
    const result = await invitationService.inviteDoctor(email, inviterUser)

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Doctor invitation sent successfully',
      token: result.token,
      expiresAt: result.expiresAt
    })

  } catch (error) {
    console.error('Error sending doctor invitation:', error)
    return NextResponse.json(
      { error: 'Failed to send invitation' },
      { status: 500 }
    )
  }
}
