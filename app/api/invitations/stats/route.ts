import { NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedApiRequest } from '../../../../lib/middleware/api-auth'

async function getInvitationStatsHandler(request: AuthenticatedApiRequest) {
  try {
    console.log(`Admin ${request.user.uid} requested invitation stats`)

    // In a real implementation, you would query the database for invitation stats
    // For now, returning mock data that matches the expected structure
    const stats = {
      totalInvitations: 15,
      pendingInvitations: 3,
      acceptedInvitations: 10,
      expiredInvitations: 2,
      recentInvitations: [
        {
          id: '1',
          email: '<EMAIL>',
          status: 'pending',
          sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days from now
        },
        {
          id: '2',
          email: '<EMAIL>',
          status: 'accepted',
          sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          acceptedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() // 12 hours ago
        },
        {
          id: '3',
          email: '<EMAIL>',
          status: 'pending',
          sentAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(), // 2 days ago
          expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days from now
        }
      ]
    }

    return NextResponse.json({
      success: true,
      data: stats,
      requestedBy: {
        uid: request.user.uid,
        role: request.user.role
      }
    })

  } catch (error) {
    console.error('Error fetching invitation stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invitation statistics' },
      { status: 500 }
    )
  }
}

export const GET = withAdminAuth(getInvitationStatsHandler)
