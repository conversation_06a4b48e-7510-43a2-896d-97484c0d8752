import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'

export async function GET(request: NextRequest) {
  try {
    // Get user ID from query params or headers (in a real app, this would come from auth middleware)
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    
    // Verify user is admin
    const user = await dataAdapter.getUserById(userId)
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    // In a real implementation, you would query the database for invitation stats
    // For now, returning mock data that matches the expected structure
    const stats = {
      totalInvitations: 15,
      pendingInvitations: 3,
      acceptedInvitations: 10,
      expiredInvitations: 2,
      recentInvitations: [
        {
          id: '1',
          email: '<EMAIL>',
          status: 'pending',
          sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days from now
        },
        {
          id: '2',
          email: '<EMAIL>',
          status: 'accepted',
          sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          acceptedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() // 12 hours ago
        },
        {
          id: '3',
          email: '<EMAIL>',
          status: 'pending',
          sentAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(), // 2 days ago
          expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days from now
        }
      ]
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Error fetching invitation stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invitation statistics' },
      { status: 500 }
    )
  }
}
