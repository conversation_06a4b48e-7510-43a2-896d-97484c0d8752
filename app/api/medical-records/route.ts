import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../lib/database'
import { with<PERSON><PERSON><PERSON><PERSON>, withDoctorAuth, AuthenticatedApiRequest, isAdmin, isDoctor } from '../../../lib/middleware/api-auth'
import { MedicalRecord, PaginationParams } from '../../../lib/types'

/**
 * GET /api/medical-records
 * Get list of medical records with role-based access control
 * - Admin: Can see all medical records
 * - Doctor: Can see medical records they created
 * - Patient: Can see their own medical records (via query parameter)
 */
async function getMedicalRecordsHandler(request: AuthenticatedApiRequest) {
  try {
    const user = request.user
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc'
    const patientId = searchParams.get('patientId')

    const paginationParams: PaginationParams = {
      page: Math.max(1, page),
      limit: Math.min(100, Math.max(1, limit)), // Limit between 1-100
      sortBy,
      sortOrder
    }

    const dataAdapter = getDatabaseAdapter()
    let result

    if (patientId) {
      // Get medical records for specific patient
      if (user.role === 'patient') {
        // Patient can only access their own records
        const patientRecord = await dataAdapter.getPatientByUserId(user.uid)
        if (!patientRecord || patientRecord.id !== patientId) {
          return NextResponse.json(
            { error: 'Access denied. You can only view your own medical records.' },
            { status: 403 }
          )
        }
      } else if (isDoctor(user) && !isAdmin(user)) {
        // TODO: Check if doctor is assigned to this patient
        // For now, doctors can see all patient records
      }

      result = await dataAdapter.getMedicalRecordsByPatient(patientId, paginationParams)
    } else {
      // Get all medical records (admin only)
      if (!isAdmin(user)) {
        return NextResponse.json(
          { error: 'Access denied. Only administrators can view all medical records.' },
          { status: 403 }
        )
      }
      result = await dataAdapter.getMedicalRecords(paginationParams)
    }

    console.log(`User ${user.uid} (${user.role}) retrieved ${result.data.length} medical records`)

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      requestedBy: {
        uid: user.uid,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching medical records:', error)
    return NextResponse.json(
      { error: 'Failed to fetch medical records' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/medical-records
 * Create a new medical record (Doctor and Admin only)
 */
async function createMedicalRecordHandler(request: AuthenticatedApiRequest) {
  try {
    const user = request.user
    const body = await request.json()

    const {
      patientId,
      date,
      diagnosis,
      treatment,
      medications,
      notes,
      attachments
    } = body

    // Validate required fields
    if (!patientId || !diagnosis) {
      return NextResponse.json(
        { error: 'Missing required fields: patientId, diagnosis' },
        { status: 400 }
      )
    }

    // Validate date
    const recordDate = date ? new Date(date) : new Date()
    if (isNaN(recordDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      )
    }

    // Check if patient exists
    const dataAdapter = getDatabaseAdapter()
    const patient = await dataAdapter.getPatientById(patientId)
    if (!patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    // Get doctor ID - if user is a doctor, use their doctor record
    let doctorId: string
    if (isAdmin(user)) {
      // Admin can create records on behalf of any doctor
      // For now, we'll need to handle this case - maybe require doctorId in request
      if (!body.doctorId) {
        return NextResponse.json(
          { error: 'Doctor ID is required when admin creates medical records' },
          { status: 400 }
        )
      }
      doctorId = body.doctorId
    } else {
      // Doctor creates record for themselves
      const doctorRecord = await dataAdapter.getDoctorByUserId(user.uid)
      if (!doctorRecord) {
        return NextResponse.json(
          { error: 'Doctor profile not found' },
          { status: 404 }
        )
      }
      doctorId = doctorRecord.id
    }

    const medicalRecordData: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'> = {
      patientId,
      doctorId,
      date: recordDate,
      diagnosis,
      treatment: treatment || '',
      medications: medications || [],
      notes: notes || '',
      attachments: attachments || []
    }

    const medicalRecord = await dataAdapter.createMedicalRecord(medicalRecordData)

    console.log(`User ${user.uid} (${user.role}) created medical record ${medicalRecord.id} for patient ${patientId}`)

    return NextResponse.json({
      success: true,
      data: medicalRecord,
      message: 'Medical record created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating medical record:', error)
    return NextResponse.json(
      { error: 'Failed to create medical record' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getMedicalRecordsHandler)
export const POST = withDoctorAuth(createMedicalRecordHandler)
