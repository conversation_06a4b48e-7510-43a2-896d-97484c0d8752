import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'
import {
  with<PERSON><PERSON><PERSON><PERSON>,
  withDoc<PERSON><PERSON><PERSON>,
  withAdmin<PERSON>uth,
  AuthenticatedApiRequest,
  isAdmin,
  isDoctor,
  isValidUUID,
  createErrorResponse
} from '../../../../lib/middleware/api-auth'
import { MedicalRecord } from '../../../../lib/types'

interface RouteContext {
  params: {
    id: string
  }
}

/**
 * Check if user can access specific medical record
 */
async function canAccessMedicalRecord(user: any, recordId: string): Promise<boolean> {
  const dataAdapter = getDatabaseAdapter()
  const record = await dataAdapter.getMedicalRecordById(recordId)
  
  if (!record) {
    return false
  }

  // Admin can access any medical record
  if (isAdmin(user)) {
    return true
  }

  // Doctor can access records they created
  if (isDoctor(user)) {
    const doctorRecord = await dataAdapter.getDoctorByUserId(user.uid)
    return doctorRecord?.id === record.doctorId
  }

  // Patient can access their own medical records
  if (user.role === 'patient') {
    const patientRecord = await dataAdapter.getPatientByUserId(user.uid)
    return patientRecord?.id === record.patientId
  }

  return false
}

/**
 * GET /api/medical-records/[id]
 * Get specific medical record by ID with role-based access control
 */
async function getMedicalRecordHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const recordId = context.params.id

    // Validate UUID format
    if (!isValidUUID(recordId)) {
      return createErrorResponse(
        'Invalid medical record ID format',
        400,
        'INVALID_RECORD_ID'
      )
    }

    // Check access permissions
    const hasAccess = await canAccessMedicalRecord(user, recordId)
    if (!hasAccess) {
      return createErrorResponse(
        'Access denied to this medical record',
        403,
        'RECORD_ACCESS_DENIED',
        { recordId, userRole: user.role }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const record = await dataAdapter.getMedicalRecordById(recordId)

    if (!record) {
      return createErrorResponse(
        'Medical record not found',
        404,
        'RECORD_NOT_FOUND'
      )
    }

    console.log(`User ${user.uid} (${user.role}) accessed medical record ${recordId}`)

    return NextResponse.json({
      success: true,
      data: record,
      requestedBy: {
        uid: user.uid,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching medical record:', error)
    return NextResponse.json(
      { error: 'Failed to fetch medical record' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/medical-records/[id]
 * Update medical record (Admin and record creator only)
 */
async function updateMedicalRecordHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const recordId = context.params.id
    const body = await request.json()

    // Validate UUID format
    if (!isValidUUID(recordId)) {
      return createErrorResponse(
        'Invalid medical record ID format',
        400,
        'INVALID_RECORD_ID'
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const existingRecord = await dataAdapter.getMedicalRecordById(recordId)

    if (!existingRecord) {
      return createErrorResponse(
        'Medical record not found',
        404,
        'RECORD_NOT_FOUND'
      )
    }

    // Check if user can update this record
    let canUpdate = false
    if (isAdmin(user)) {
      canUpdate = true
    } else if (isDoctor(user)) {
      // Doctor can only update records they created
      const doctorRecord = await dataAdapter.getDoctorByUserId(user.uid)
      canUpdate = doctorRecord?.id === existingRecord.doctorId
    }

    if (!canUpdate) {
      return createErrorResponse(
        'Access denied. You can only update medical records you created.',
        403,
        'UPDATE_ACCESS_DENIED',
        { recordId, userRole: user.role }
      )
    }

    // Validate and sanitize update data
    const updateData: Partial<MedicalRecord> = {}
    
    if (body.date) {
      const recordDate = new Date(body.date)
      if (isNaN(recordDate.getTime())) {
        return createErrorResponse(
          'Invalid date format',
          400,
          'INVALID_DATE'
        )
      }
      updateData.date = recordDate
    }
    
    if (body.diagnosis) updateData.diagnosis = body.diagnosis
    if (body.treatment !== undefined) updateData.treatment = body.treatment
    if (body.medications !== undefined) updateData.medications = body.medications
    if (body.notes !== undefined) updateData.notes = body.notes
    if (body.attachments !== undefined) updateData.attachments = body.attachments

    // Only admins can change patient or doctor assignments
    if (isAdmin(user)) {
      if (body.patientId) {
        // Verify patient exists
        const patient = await dataAdapter.getPatientById(body.patientId)
        if (!patient) {
          return createErrorResponse(
            'Patient not found',
            404,
            'PATIENT_NOT_FOUND'
          )
        }
        updateData.patientId = body.patientId
      }
      if (body.doctorId) {
        // Verify doctor exists
        const doctor = await dataAdapter.getDoctorById(body.doctorId)
        if (!doctor) {
          return createErrorResponse(
            'Doctor not found',
            404,
            'DOCTOR_NOT_FOUND'
          )
        }
        updateData.doctorId = body.doctorId
      }
    } else if (body.patientId || body.doctorId) {
      return createErrorResponse(
        'Only administrators can change patient or doctor assignments',
        403,
        'ASSIGNMENT_ACCESS_DENIED'
      )
    }

    const updatedRecord = await dataAdapter.updateMedicalRecord(recordId, updateData)

    console.log(`User ${user.uid} (${user.role}) updated medical record ${recordId}`)

    return NextResponse.json({
      success: true,
      data: updatedRecord,
      message: 'Medical record updated successfully'
    })

  } catch (error) {
    console.error('Error updating medical record:', error)
    return NextResponse.json(
      { error: 'Failed to update medical record' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/medical-records/[id]
 * Delete medical record (Admin only)
 */
async function deleteMedicalRecordHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const recordId = context.params.id

    // Validate UUID format
    if (!isValidUUID(recordId)) {
      return createErrorResponse(
        'Invalid medical record ID format',
        400,
        'INVALID_RECORD_ID'
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const record = await dataAdapter.getMedicalRecordById(recordId)

    if (!record) {
      return createErrorResponse(
        'Medical record not found',
        404,
        'RECORD_NOT_FOUND'
      )
    }

    await dataAdapter.deleteMedicalRecord(recordId)

    console.log(`Admin ${user.uid} deleted medical record ${recordId}`)

    return NextResponse.json({
      success: true,
      message: 'Medical record deleted successfully',
      deletedRecord: {
        id: recordId,
        patientId: record.patientId,
        diagnosis: record.diagnosis,
        date: record.date
      }
    })

  } catch (error) {
    console.error('Error deleting medical record:', error)
    return NextResponse.json(
      { error: 'Failed to delete medical record' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getMedicalRecordHandler)
export const PUT = withUserAuth(updateMedicalRecordHandler)
export const DELETE = withAdminAuth(deleteMedicalRecordHandler)
