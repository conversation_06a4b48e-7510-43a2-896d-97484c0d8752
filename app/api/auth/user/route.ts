import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'
import { withUserAuth, AuthenticatedApiRequest, isAdmin } from '../../../../lib/middleware/api-auth'

async function getUserHandler(request: AuthenticatedApiRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const firebaseUid = searchParams.get('firebaseUid')

    // If no firebaseUid provided, return the authenticated user's own data
    if (!firebaseUid) {
      return NextResponse.json({ user: request.user })
    }

    // Get the authenticated user's Firebase UID from the decoded token
    const authenticatedFirebaseUid = request.decodedToken?.uid

    // Check if user is trying to access someone else's data
    if (firebaseUid !== authenticatedFirebaseUid && !isAdmin(request.user)) {
      return NextResponse.json(
        { error: 'Access denied. You can only access your own user data.' },
        { status: 403 }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const user = await dataAdapter.getUserByFirebaseUid(firebaseUid)

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ user })
  } catch (error) {
    console.error('Error getting user:', error)
    return NextResponse.json(
      { error: 'Failed to get user' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getUserHandler)
