import { NextRequest, NextResponse } from 'next/server'
import { RegisterData } from '../../../../lib/types'
import { getDatabaseAdapter } from '../../../../lib/database'
import InvitationService from '../../../../lib/services/invitation.service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userData, invitationToken } = body

    const dataAdapter = getDatabaseAdapter()

    if (invitationToken) {
      // Validate invitation for doctor registration
      const invitationService = new InvitationService()
      const validation = await invitationService.validateInvitation(invitationToken)
      
      if (!validation.isValid || !validation.invitation) {
        return NextResponse.json(
          { error: validation.message },
          { status: 400 }
        )
      }

      // Ensure email matches invitation
      if (userData.email !== validation.invitation.email) {
        return NextResponse.json(
          { error: 'Email must match the invitation' },
          { status: 400 }
        )
      }

      // Create doctor user
      const user = await dataAdapter.createUser({
        firebaseUid: userData.firebaseUid,
        email: userData.email,
        displayName: userData.displayName,
        role: 'doctor',
        createdAt: new Date(),
        updatedAt: new Date()
      })

      // Mark invitation as used
      await invitationService.markInvitationAsUsed(invitationToken)

      return NextResponse.json({ user })
    } else {
      // Regular patient registration
      const user = await dataAdapter.createUser({
        firebaseUid: userData.firebaseUid,
        email: userData.email,
        displayName: userData.displayName,
        role: 'patient',
        createdAt: new Date(),
        updatedAt: new Date()
      })

      return NextResponse.json({ user })
    }
  } catch (error) {
    console.error('Error in register API:', error)
    return NextResponse.json(
      { error: 'Failed to register user' },
      { status: 500 }
    )
  }
}
