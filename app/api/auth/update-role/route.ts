import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '../../../../lib/types'
import { getDatabaseAdapter } from '../../../../lib/database'
import { withAdminAuth, AuthenticatedApiRequest } from '../../../../lib/middleware/api-auth'

async function updateUserRoleHandler(request: AuthenticatedApiRequest) {
  try {
    const { firebaseUid, role } = await request.json()

    if (!firebaseUid || !role) {
      return NextResponse.json(
        { error: 'Firebase UID and role are required' },
        { status: 400 }
      )
    }

    // Validate role value
    const validRoles: UserRole[] = ['admin', 'doctor', 'patient']
    if (!validRoles.includes(role as UserRole)) {
      return NextResponse.json(
        { error: `Invalid role. Must be one of: ${validRoles.join(', ')}` },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Find user by Firebase UID first
    const targetUser = await dataAdapter.getUserByFirebaseUid(firebaseUid)
    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Prevent admins from changing their own role to non-admin
    if (request.user.uid === targetUser.uid && role !== 'admin') {
      return NextResponse.json(
        { error: 'Cannot change your own admin role' },
        { status: 403 }
      )
    }

    await dataAdapter.updateUserRole(targetUser.uid, role as UserRole)

    console.log(`Admin ${request.user.uid} updated user ${targetUser.uid} role from ${targetUser.role} to ${role}`)

    return NextResponse.json({
      success: true,
      message: `User role updated to ${role}`,
      user: {
        uid: targetUser.uid,
        email: targetUser.email,
        displayName: targetUser.displayName,
        role: role as UserRole
      }
    })
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: 'Failed to update user role' },
      { status: 500 }
    )
  }
}

export const POST = withAdminAuth(updateUserRoleHandler)
