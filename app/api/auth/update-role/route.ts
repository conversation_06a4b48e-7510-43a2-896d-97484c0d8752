import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '../../../../lib/types'
import { getDatabaseAdapter } from '../../../../lib/database'

export async function POST(request: NextRequest) {
  try {
    const { firebaseUid, role } = await request.json()

    if (!firebaseUid || !role) {
      return NextResponse.json(
        { error: 'Firebase UID and role are required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    
    // Find user by Firebase UID first
    const user = await dataAdapter.getUserByFirebaseUid(firebaseUid)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    await dataAdapter.updateUserRole(user.uid, role as UserRole)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: 'Failed to update user role' },
      { status: 500 }
    )
  }
}
