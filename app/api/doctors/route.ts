import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../lib/database'
import { with<PERSON><PERSON><PERSON><PERSON>, withAdminAuth, AuthenticatedApiRequest, isAdmin, isDoctor } from '../../../lib/middleware/api-auth'
import { Doctor, PaginationParams } from '../../../lib/types'

/**
 * GET /api/doctors
 * Get list of doctors with role-based access control
 * - Admin: Can see all doctors
 * - Doctor: Can see all doctors (for collaboration purposes)
 * - Patient: Can see all doctors (for appointment booking)
 */
async function getDoctorsHandler(request: AuthenticatedApiRequest) {
  try {
    const user = request.user

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc'

    const paginationParams: PaginationParams = {
      page: Math.max(1, page),
      limit: Math.min(100, Math.max(1, limit)), // Limit between 1-100
      sortBy,
      sortOrder
    }

    const dataAdapter = getDatabaseAdapter()
    const result = await dataAdapter.getDoctors(paginationParams)

    console.log(`User ${user.uid} (${user.role}) retrieved ${result.data.length} doctors`)

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      requestedBy: {
        uid: user.uid,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching doctors:', error)
    return NextResponse.json(
      { error: 'Failed to fetch doctors' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/doctors
 * Create a new doctor (Admin only)
 */
async function createDoctorHandler(request: AuthenticatedApiRequest) {
  try {
    const user = request.user
    const body = await request.json()

    const {
      firstName,
      lastName,
      email,
      phone,
      specialization,
      licenseNumber,
      department,
      experience,
      education,
      schedule
    } = body

    // Validate required fields
    if (!firstName || !lastName || !email || !specialization || !licenseNumber || !department) {
      return NextResponse.json(
        { error: 'Missing required fields: firstName, lastName, email, specialization, licenseNumber, department' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate experience
    if (experience !== undefined && (typeof experience !== 'number' || experience < 0)) {
      return NextResponse.json(
        { error: 'Experience must be a non-negative number' },
        { status: 400 }
      )
    }

    const doctorData: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'> = {
      firstName,
      lastName,
      email,
      phone: phone || '',
      specialization,
      licenseNumber,
      department,
      experience: experience || 0,
      education: education || [],
      patients: [], // Start with no assigned patients
      schedule: schedule || []
    }

    const dataAdapter = getDatabaseAdapter()
    const doctor = await dataAdapter.createDoctor(doctorData)

    console.log(`Admin ${user.uid} created doctor ${doctor.id}`)

    return NextResponse.json({
      success: true,
      data: doctor,
      message: 'Doctor created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating doctor:', error)
    return NextResponse.json(
      { error: 'Failed to create doctor' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getDoctorsHandler)
export const POST = withAdminAuth(createDoctorHandler)
