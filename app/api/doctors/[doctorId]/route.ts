import { NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'
import { 
  with<PERSON><PERSON><PERSON><PERSON>, 
  withAd<PERSON><PERSON>uth, 
  AuthenticatedApiRequest, 
  isAdmin, 
  isDoctor,
  isValidUUID,
  createErrorResponse
} from '../../../../lib/middleware/api-auth'
import { Doctor } from '../../../../lib/types'

interface RouteContext {
  params: {
    doctorId: string
  }
}

/**
 * Check if user can access specific doctor data
 */
async function canAccessDoctor(user: any, doctorId: string): Promise<boolean> {
  // Ad<PERSON> can access any doctor
  if (isAdmin(user)) {
    return true
  }

  // Doctor can access their own data
  if (isDoctor(user)) {
    const dataAdapter = getDatabaseAdapter()
    const doctor = await dataAdapter.getDoctorByUserId(user.uid)
    return doctor?.id === doctorId
  }

  // Patients can view doctor information (for appointment booking)
  if (user.role === 'patient') {
    return true
  }

  return false
}

/**
 * GET /api/doctors/[doctorId]
 * Get specific doctor by ID with role-based access control
 */
async function getDoctorHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const doctorId = context.params.doctorId

    // Validate UUID format
    if (!isValidUUID(doctorId)) {
      return createErrorResponse(
        'Invalid doctor ID format',
        400,
        'INVALID_DOCTOR_ID'
      )
    }

    // Check access permissions
    const hasAccess = await canAccessDoctor(user, doctorId)
    if (!hasAccess) {
      return createErrorResponse(
        'Access denied to this doctor',
        403,
        'DOCTOR_ACCESS_DENIED',
        { doctorId, userRole: user.role }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const doctor = await dataAdapter.getDoctorById(doctorId)

    if (!doctor) {
      return createErrorResponse(
        'Doctor not found',
        404,
        'DOCTOR_NOT_FOUND'
      )
    }

    // For patients, hide sensitive information like patient list
    let responseData = doctor
    if (user.role === 'patient') {
      responseData = {
        ...doctor,
        patients: [] // Hide patient list from patients
      }
    }

    console.log(`User ${user.uid} (${user.role}) accessed doctor ${doctorId}`)

    return NextResponse.json({
      success: true,
      data: responseData,
      requestedBy: {
        uid: user.uid,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching doctor:', error)
    return NextResponse.json(
      { error: 'Failed to fetch doctor' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/doctors/[doctorId]
 * Update doctor information (Admin and the doctor themselves)
 */
async function updateDoctorHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const doctorId = context.params.doctorId
    const body = await request.json()

    // Validate UUID format
    if (!isValidUUID(doctorId)) {
      return createErrorResponse(
        'Invalid doctor ID format',
        400,
        'INVALID_DOCTOR_ID'
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const existingDoctor = await dataAdapter.getDoctorById(doctorId)

    if (!existingDoctor) {
      return createErrorResponse(
        'Doctor not found',
        404,
        'DOCTOR_NOT_FOUND'
      )
    }

    // Check if user can update this doctor
    let canUpdate = false
    if (isAdmin(user)) {
      canUpdate = true
    } else if (isDoctor(user)) {
      // Doctor can only update their own profile
      const userDoctor = await dataAdapter.getDoctorByUserId(user.uid)
      canUpdate = userDoctor?.id === doctorId
    }

    if (!canUpdate) {
      return createErrorResponse(
        'Access denied. You can only update your own doctor profile.',
        403,
        'UPDATE_ACCESS_DENIED',
        { doctorId, userRole: user.role }
      )
    }

    // Validate and sanitize update data
    const updateData: Partial<Doctor> = {}
    
    if (body.firstName) updateData.firstName = body.firstName
    if (body.lastName) updateData.lastName = body.lastName
    if (body.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(body.email)) {
        return createErrorResponse(
          'Invalid email format',
          400,
          'INVALID_EMAIL'
        )
      }
      updateData.email = body.email
    }
    if (body.phone) updateData.phone = body.phone
    if (body.specialization) updateData.specialization = body.specialization
    if (body.department) updateData.department = body.department
    if (body.experience !== undefined) {
      if (typeof body.experience !== 'number' || body.experience < 0) {
        return createErrorResponse(
          'Experience must be a non-negative number',
          400,
          'INVALID_EXPERIENCE'
        )
      }
      updateData.experience = body.experience
    }
    if (body.education) updateData.education = body.education
    if (body.schedule) updateData.schedule = body.schedule

    // Only admins can update license number and patient assignments
    if (isAdmin(user)) {
      if (body.licenseNumber) updateData.licenseNumber = body.licenseNumber
      if (body.patients) updateData.patients = body.patients
    } else if (body.licenseNumber || body.patients) {
      return createErrorResponse(
        'Only administrators can update license number and patient assignments',
        403,
        'ADMIN_ONLY_FIELDS'
      )
    }

    const updatedDoctor = await dataAdapter.updateDoctor(doctorId, updateData)

    console.log(`User ${user.uid} (${user.role}) updated doctor ${doctorId}`)

    return NextResponse.json({
      success: true,
      data: updatedDoctor,
      message: 'Doctor updated successfully'
    })

  } catch (error) {
    console.error('Error updating doctor:', error)
    return NextResponse.json(
      { error: 'Failed to update doctor' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/doctors/[doctorId]
 * Delete doctor (Admin only)
 */
async function deleteDoctorHandler(request: AuthenticatedApiRequest, context: RouteContext) {
  try {
    const user = request.user
    const doctorId = context.params.doctorId

    // Validate UUID format
    if (!isValidUUID(doctorId)) {
      return createErrorResponse(
        'Invalid doctor ID format',
        400,
        'INVALID_DOCTOR_ID'
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const doctor = await dataAdapter.getDoctorById(doctorId)

    if (!doctor) {
      return createErrorResponse(
        'Doctor not found',
        404,
        'DOCTOR_NOT_FOUND'
      )
    }

    await dataAdapter.deleteDoctor(doctorId)

    console.log(`Admin ${user.uid} deleted doctor ${doctorId}`)

    return NextResponse.json({
      success: true,
      message: 'Doctor deleted successfully',
      deletedDoctor: {
        id: doctorId,
        name: `${doctor.firstName} ${doctor.lastName}`,
        specialization: doctor.specialization
      }
    })

  } catch (error) {
    console.error('Error deleting doctor:', error)
    return NextResponse.json(
      { error: 'Failed to delete doctor' },
      { status: 500 }
    )
  }
}

export const GET = withUserAuth(getDoctorHandler)
export const PUT = withUserAuth(updateDoctorHandler)
export const DELETE = withAdminAuth(deleteDoctorHandler)
